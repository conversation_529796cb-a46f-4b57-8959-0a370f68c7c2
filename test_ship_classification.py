"""
测试重构后的船舶分类系统
验证新的3个船长区间：100m以下、100-200m、200m以上
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def test_ship_classification():
    """测试船舶分类逻辑"""
    print("🚢 测试重构后的船舶分类系统")
    print("=" * 60)
    
    # 新的船长区间定义
    length_intervals = [
        (0, 100, "100m以下"),
        (100, 200, "100-200m"),
        (200, 500, "200m以上")
    ]
    
    print("新的船长分类区间:")
    for min_len, max_len, interval_name in length_intervals:
        print(f"   {interval_name}: [{min_len}, {max_len}) 米")
    
    # 生成测试船舶数据
    test_ships = generate_test_ship_data()
    
    print(f"\n生成测试船舶数据: {len(test_ships)} 艘")
    
    # 分类统计
    classification_stats = {}
    for _, _, interval_name in length_intervals:
        classification_stats[interval_name] = []
    
    unclassified_ships = []
    
    for ship in test_ships:
        ship_length = ship['length']
        classified = False
        
        for min_len, max_len, interval_name in length_intervals:
            if min_len <= ship_length < max_len:
                classification_stats[interval_name].append(ship)
                classified = True
                break
        
        if not classified:
            unclassified_ships.append(ship)
    
    # 打印分类结果
    print(f"\n📊 船舶分类统计:")
    total_classified = 0
    for interval_name, ships in classification_stats.items():
        count = len(ships)
        total_classified += count
        percentage = count / len(test_ships) * 100
        
        if count > 0:
            lengths = [ship['length'] for ship in ships]
            min_length = min(lengths)
            max_length = max(lengths)
            avg_length = np.mean(lengths)
            
            print(f"   {interval_name}: {count}艘 ({percentage:.1f}%)")
            print(f"      长度范围: {min_length:.1f}m - {max_length:.1f}m")
            print(f"      平均长度: {avg_length:.1f}m")
        else:
            print(f"   {interval_name}: {count}艘 ({percentage:.1f}%)")
    
    print(f"   未分类: {len(unclassified_ships)}艘")
    print(f"   分类覆盖率: {total_classified/len(test_ships)*100:.1f}%")
    
    # 可视化分类结果
    visualize_classification(classification_stats, length_intervals)
    
    # 与原分类对比
    compare_with_old_classification(test_ships)
    
    return True

def generate_test_ship_data():
    """生成测试船舶数据"""
    np.random.seed(42)
    
    # 模拟真实的船舶长度分布
    ships = []
    
    # 小型船舶 (20-100m) - 较多
    small_ships = np.random.uniform(20, 100, 150)
    for length in small_ships:
        ships.append({
            'mmsi': f"S{len(ships)+1:04d}",
            'length': length,
            'type': '小型船舶'
        })
    
    # 中型船舶 (100-200m) - 中等数量
    medium_ships = np.random.uniform(100, 200, 100)
    for length in medium_ships:
        ships.append({
            'mmsi': f"M{len(ships)+1:04d}",
            'length': length,
            'type': '中型船舶'
        })
    
    # 大型船舶 (200-400m) - 较少
    large_ships = np.random.uniform(200, 400, 50)
    for length in large_ships:
        ships.append({
            'mmsi': f"L{len(ships)+1:04d}",
            'length': length,
            'type': '大型船舶'
        })
    
    # 超大型船舶 (400-500m) - 很少
    xlarge_ships = np.random.uniform(400, 500, 10)
    for length in xlarge_ships:
        ships.append({
            'mmsi': f"XL{len(ships)+1:04d}",
            'length': length,
            'type': '超大型船舶'
        })
    
    return ships

def visualize_classification(classification_stats, length_intervals):
    """可视化分类结果"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('重构后的船舶分类分析', fontsize=16, fontweight='bold')
    
    # 1. 分类数量柱状图
    ax1 = axes[0, 0]
    categories = list(classification_stats.keys())
    counts = [len(ships) for ships in classification_stats.values()]
    
    bars = ax1.bar(categories, counts, color=['lightblue', 'lightgreen', 'lightcoral'])
    ax1.set_title('各类别船舶数量')
    ax1.set_ylabel('船舶数量')
    
    # 在柱状图上添加数值标签
    for bar, count in zip(bars, counts):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height,
                f'{count}', ha='center', va='bottom')
    
    # 2. 分类比例饼图
    ax2 = axes[0, 1]
    colors = ['lightblue', 'lightgreen', 'lightcoral']
    wedges, texts, autotexts = ax2.pie(counts, labels=categories, colors=colors, 
                                      autopct='%1.1f%%', startangle=90)
    ax2.set_title('船舶分类比例')
    
    # 3. 船长分布直方图
    ax3 = axes[1, 0]
    all_lengths = []
    colors_hist = []
    labels_hist = []
    
    for i, (category, ships) in enumerate(classification_stats.items()):
        if ships:
            lengths = [ship['length'] for ship in ships]
            all_lengths.extend(lengths)
            ax3.hist(lengths, bins=20, alpha=0.7, color=colors[i], 
                    label=f'{category} ({len(ships)}艘)')
    
    ax3.set_title('船长分布直方图')
    ax3.set_xlabel('船长 (m)')
    ax3.set_ylabel('频次')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 分类边界可视化
    ax4 = axes[1, 1]
    
    # 绘制分类边界线
    boundaries = [0, 100, 200, 500]
    boundary_names = ['0m', '100m', '200m', '500m']
    
    for i, boundary in enumerate(boundaries):
        ax4.axvline(x=boundary, color='red', linestyle='--', alpha=0.7)
        ax4.text(boundary, 0.8, boundary_names[i], rotation=90, 
                ha='right', va='bottom', fontsize=10)
    
    # 绘制船长分布
    if all_lengths:
        ax4.hist(all_lengths, bins=50, alpha=0.6, color='skyblue', density=True)
    
    ax4.set_title('分类边界与船长分布')
    ax4.set_xlabel('船长 (m)')
    ax4.set_ylabel('密度')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    save_path = Path("vis/ship_classification_analysis.png")
    save_path.parent.mkdir(exist_ok=True)
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📊 分类分析图保存至: {save_path}")

def compare_with_old_classification(test_ships):
    """与原分类系统对比"""
    print(f"\n🔄 与原分类系统对比")
    print("=" * 40)
    
    # 原分类系统 (5个区间)
    old_intervals = [
        (0, 50, "0-50m"),
        (50, 100, "50-100m"),
        (100, 150, "100-150m"),
        (150, 200, "150-200m"),
        (200, 250, "200-250m")
    ]
    
    # 新分类系统 (3个区间)
    new_intervals = [
        (0, 100, "100m以下"),
        (100, 200, "100-200m"),
        (200, 500, "200m以上")
    ]
    
    # 统计对比
    old_stats = classify_ships(test_ships, old_intervals)
    new_stats = classify_ships(test_ships, new_intervals)
    
    print("原分类系统 (5个区间):")
    for interval_name, count in old_stats.items():
        percentage = count / len(test_ships) * 100
        print(f"   {interval_name}: {count}艘 ({percentage:.1f}%)")
    
    print(f"\n新分类系统 (3个区间):")
    for interval_name, count in new_stats.items():
        percentage = count / len(test_ships) * 100
        print(f"   {interval_name}: {count}艘 ({percentage:.1f}%)")
    
    # 分析优势
    print(f"\n📈 新分类系统优势:")
    print(f"   1. 区间数量减少: 5个 → 3个 (简化60%)")
    print(f"   2. 覆盖范围扩大: 250m → 500m (扩大100%)")
    print(f"   3. 更符合实际: 大型船舶统一归类")
    
    # 计算数据分布均匀性
    old_counts = list(old_stats.values())
    new_counts = list(new_stats.values())
    
    old_cv = np.std(old_counts) / np.mean(old_counts) if np.mean(old_counts) > 0 else 0
    new_cv = np.std(new_counts) / np.mean(new_counts) if np.mean(new_counts) > 0 else 0
    
    print(f"   4. 分布均匀性: CV从{old_cv:.3f}改善到{new_cv:.3f}")
    
    return True

def classify_ships(ships, intervals):
    """按给定区间分类船舶"""
    stats = {}
    
    for _, _, interval_name in intervals:
        stats[interval_name] = 0
    
    for ship in ships:
        ship_length = ship['length']
        
        for min_len, max_len, interval_name in intervals:
            if min_len <= ship_length < max_len:
                stats[interval_name] += 1
                break
    
    return stats

def generate_classification_summary():
    """生成分类总结报告"""
    print(f"\n📋 船舶分类重构总结")
    print("=" * 60)
    
    print("🔄 变更内容:")
    print("   原分类: 0-50m, 50-100m, 100-150m, 150-200m, 200-250m")
    print("   新分类: 100m以下, 100-200m, 200m以上")
    
    print(f"\n✅ 改进效果:")
    print("   1. 简化分类: 5个区间 → 3个区间")
    print("   2. 扩大覆盖: 最大250m → 最大500m")
    print("   3. 更合理: 符合船舶实际尺寸分布")
    print("   4. 易理解: 分类边界更直观")
    
    print(f"\n🎯 应用场景:")
    print("   • 100m以下: 小型船舶 (渔船、游艇、小货船)")
    print("   • 100-200m: 中型船舶 (中型货船、客船)")
    print("   • 200m以上: 大型船舶 (大型货船、集装箱船、油轮)")
    
    print(f"\n📁 影响文件:")
    print("   • probability_density_analysis.py: 更新分类定义")
    print("   • ship_domain_fitting.py: 自动适配新分类")
    print("   • 所有输出结果: 使用新的分类标签")

def main():
    """主函数"""
    print("🚢 船舶分类重构测试系统")
    print("=" * 60)
    
    # 创建输出目录
    Path("vis").mkdir(exist_ok=True)
    
    try:
        # 运行测试
        success = test_ship_classification()
        
        if success:
            generate_classification_summary()
            print(f"\n🎉 船舶分类重构测试完成！")
            print(f"📊 分析图表: vis/ship_classification_analysis.png")
        else:
            print(f"\n❌ 测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
