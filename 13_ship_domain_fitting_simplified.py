"""
船舶领域拟合系统 - 基于机动时刻数据的简化版
基于12.avoidance_scene_extraction.py的输出，拟合船舶领域椭圆边界

核心特性：
- 直接利用12的机动时刻数据输出
- 简化的数据处理流程
- 保持原有的椭圆拟合逻辑

数据输入：
- crossing_avoidance_scenes.pkl：交叉避让机动时刻
- overtaking_avoidance_scenes.pkl：追越避让机动时刻

功能：
1. 从机动时刻数据计算相对位置
2. 按船型分类：0-50m, 50-100m, 100-150m, 150-200m, 200-250m
3. 拟合椭圆边界：长半轴为船头方向，短半轴为横向方向

使用方法：
python 13_ship_domain_fitting_simplified.py
"""

import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from tqdm import tqdm
import math
import time
from scipy.optimize import leastsq

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


class SimplifiedShipDomainFitting:
    """基于机动时刻数据的简化船舶领域拟合器"""

    def __init__(self, data_months=['2024_1', '2024_2', '2024_3'], debug=False):
        """
        初始化船舶领域拟合器

        :param data_months: 数据月份列表，支持多个月份
        :param debug: 是否开启调试模式
        """
        self.data_months = data_months if isinstance(data_months, list) else [data_months]
        self.debug = debug

        # 船长区间定义
        self.length_intervals = [
            (0, 50, "0-50m"),
            (50, 100, "50-100m"),
            (100, 150, "100-150m"),
            (150, 200, "150-200m"),
            (200, 250, "200-250m")
        ]

        # 机动时刻数据存储
        self.crossing_moments = []
        self.overtaking_moments = []

        # 按船型和场景类型分类的相对位置数据
        self.crossing_relative_data = {}
        self.overtaking_relative_data = {}

        # 椭圆拟合结果
        self.crossing_ellipse_params = {}
        self.overtaking_ellipse_params = {}

        print(f"🚢 简化船舶领域拟合器初始化完成")
        print(f"   数据月份: {self.data_months}")
        print(f"   船长区间: {[name for _, _, name in self.length_intervals]}")

    def load_maneuvering_moments(self):
        """加载机动时刻数据"""
        print("\n=== 加载机动时刻数据 ===")

        total_crossing = 0
        total_overtaking = 0

        for data_month in self.data_months:
            print(f"\n--- 加载 {data_month} 数据 ---")

            # 加载交叉避让机动时刻
            crossing_file = f'result/{data_month}/crossing_avoidance_scenes.pkl'
            if Path(crossing_file).exists():
                with open(crossing_file, 'rb') as f:
                    moments = pickle.load(f)
                self.crossing_moments.extend(moments)
                total_crossing += len(moments)
                print(f"   交叉避让: {len(moments)} 个时刻")
            else:
                print(f"⚠️  未找到: {crossing_file}")

            # 加载追越避让机动时刻（仅使用最后一个月）
            if data_month == self.data_months[-1]:
                overtaking_file = f'result/{data_month}/overtaking_avoidance_scenes.pkl'
                if Path(overtaking_file).exists():
                    with open(overtaking_file, 'rb') as f:
                        moments = pickle.load(f)
                    self.overtaking_moments.extend(moments)
                    total_overtaking += len(moments)
                    print(f"   追越避让: {len(moments)} 个时刻（仅使用最后月份）")
                else:
                    print(f"⚠️  未找到: {overtaking_file}")

        print(f"\n✅ 机动时刻加载完成:")
        print(f"   交叉避让: {total_crossing} 个")
        print(f"   追越避让: {total_overtaking} 个")

    def extract_relative_positions(self):
        """从机动时刻提取相对位置数据"""
        print("\n=== 提取相对位置数据 ===")

        # 初始化数据结构
        for _, _, interval_name in self.length_intervals:
            self.crossing_relative_data[interval_name] = []
            self.overtaking_relative_data[interval_name] = []

        # 处理交叉避让
        print("处理交叉避让机动时刻...")
        crossing_processed = self._process_moments(self.crossing_moments, self.crossing_relative_data)

        # 处理追越避让
        print("处理追越避让机动时刻...")
        overtaking_processed = self._process_moments(self.overtaking_moments, self.overtaking_relative_data)

        print(f"✅ 相对位置提取完成:")
        print(f"   交叉避让: {crossing_processed} 个有效相对位置")
        print(f"   追越避让: {overtaking_processed} 个有效相对位置")

        # 打印统计
        self._print_data_stats()

    def _process_moments(self, moments, target_data_dict):
        """处理机动时刻，提取相对位置"""
        processed_count = 0

        for moment in tqdm(moments, desc="提取相对位置"):
            try:
                # 提取机动船和目标船
                maneuvering_ship = moment[0]  # 机动船（本船）
                target_ship = moment[1]  # 被避让船（目标船）

                # 数据质量检查
                if not self._validate_ship_data(maneuvering_ship, target_ship):
                    continue

                # 获取船长区间
                own_length = maneuvering_ship.get('length', 100.0)
                interval_name = self._get_length_interval(own_length)

                if interval_name is None:
                    continue

                # 计算相对位置
                relative_pos = self._calculate_relative_position(maneuvering_ship, target_ship)

                if relative_pos is not None and self._validate_relative_position(relative_pos):
                    target_data_dict[interval_name].append(relative_pos)
                    processed_count += 1

            except Exception as e:
                if self.debug:
                    print(f"处理机动时刻失败: {e}")
                continue

        return processed_count

    def _validate_ship_data(self, ship1, ship2):
        """验证船舶数据质量"""
        # 检查必要字段
        required_fields = ['mmsi', 'lon', 'lat', 'cog', 'sog', 'length']
        for ship in [ship1, ship2]:
            for field in required_fields:
                if field not in ship or ship[field] is None:
                    return False

        # 检查数值合理性
        if (abs(ship1['lon']) > 180 or abs(ship1['lat']) > 90 or
                abs(ship2['lon']) > 180 or abs(ship2['lat']) > 90):
            return False

        # 检查船舶尺寸合理性
        if (ship1['length'] <= 0 or ship1['length'] > 500 or
                ship2['length'] <= 0 or ship2['length'] > 500):
            return False

        # 检查速度合理性（0-30节）
        if (ship1['sog'] < 0 or ship1['sog'] > 30 or
                ship2['sog'] < 0 or ship2['sog'] > 30):
            return False

        return True

    def _validate_relative_position(self, relative_pos):
        """验证相对位置的合理性"""
        # 取消所有边界值筛选，只保留基本的数据完整性检查
        distance = relative_pos['distance']

        # 只检查距离是否为正数
        if distance <= 0:
            return False

        return True

    def _calculate_relative_position(self, own_ship, target_ship):
        """计算相对位置"""
        try:
            # 经纬度转米（简化计算）
            lat_to_m = 111000
            lon_to_m = 111000 * math.cos(math.radians(own_ship['lat']))

            # 计算地理坐标差
            delta_lon = target_ship['lon'] - own_ship['lon']
            delta_lat = target_ship['lat'] - own_ship['lat']

            # 转为米制坐标
            x_geo = delta_lon * lon_to_m
            y_geo = delta_lat * lat_to_m

            # 转换到本船坐标系
            x_rel, y_rel = self._convert_to_ship_coordinate(x_geo, y_geo, own_ship['cog'])

            # 计算距离
            distance = math.sqrt(x_rel ** 2 + y_rel ** 2)

            return {
                'relative_x': x_rel,
                'relative_y': y_rel,
                'distance': distance,
                'own_ship_mmsi': own_ship['mmsi'],
                'target_ship_mmsi': target_ship['mmsi'],
                'own_ship_length': own_ship.get('length', 100.0),
                'time_point': own_ship['time_point']
            }

        except Exception as e:
            if self.debug:
                print(f"计算相对位置失败: {e}")
            return None

    @staticmethod
    def _convert_to_ship_coordinate(x_geo, y_geo, heading):
        """转换到船舶坐标系（船头向前为Y轴正方向）"""
        heading_rad = math.radians(-heading)
        cos_h = math.cos(heading_rad)
        sin_h = math.sin(heading_rad)

        x_ship = cos_h * x_geo + sin_h * y_geo
        y_ship = -sin_h * x_geo + cos_h * y_geo

        return x_ship, y_ship

    def _get_length_interval(self, length):
        """根据船长获取区间名称"""
        for min_len, max_len, interval_name in self.length_intervals:
            if min_len <= length < max_len:
                return interval_name
        return None

    def _print_data_stats(self):
        """打印数据统计"""
        print(f"\n📊 相对位置数据统计:")
        for _, _, interval_name in self.length_intervals:
            crossing_count = len(self.crossing_relative_data[interval_name])
            overtaking_count = len(self.overtaking_relative_data[interval_name])
            print(f"   {interval_name}: 交叉{crossing_count}个, 追越{overtaking_count}个")

    def fit_ellipses(self):
        """拟合椭圆边界"""
        print("\n=== 拟合椭圆边界 ===")

        # 拟合交叉避让椭圆
        print("拟合交叉避让椭圆...")
        self._fit_scenario_ellipses(self.crossing_relative_data, self.crossing_ellipse_params, "交叉")

        # 拟合追越避让椭圆
        print("拟合追越避让椭圆...")
        self._fit_scenario_ellipses(self.overtaking_relative_data, self.overtaking_ellipse_params, "追越")

        print("✅ 椭圆拟合完成")

    def _fit_ellipse(self, x_coords, y_coords):
        """椭圆拟合 - 使用密度边界检测方法"""
        try:
            x_coords = np.array(x_coords)
            y_coords = np.array(y_coords)

            if len(x_coords) < 10:
                return None

            # 使用密度边界检测方法
            boundary_result = self._detect_density_boundary(x_coords, y_coords)

            if boundary_result is None:
                # 如果边界检测失败，回退到传统方法
                return self._fit_ellipse_traditional(x_coords, y_coords)

            return boundary_result

        except Exception as e:
            if self.debug:
                print(f"椭圆拟合失败: {e}")
            return None

    def _detect_density_boundary(self, x_coords, y_coords):
        """密度边界检测 - 基于平均值初始化，逐步扩展找到突变点"""
        try:
            # 1. 根据平均值初始化椭圆
            initial_ellipse = self._initialize_ellipse_from_mean(x_coords, y_coords)

            if initial_ellipse is None:
                return None

            # 2. 逐步扩展找到突变点
            expansion_points = self._expand_and_find_mutation_points(x_coords, y_coords, initial_ellipse)

            if expansion_points is None or len(expansion_points) < 8:
                return None

            # 3. 基于突变点重新拟合椭圆
            result = self._fit_ellipse_from_mutation_points(expansion_points)

            if result is not None:
                result['initial_ellipse'] = initial_ellipse
                result['mutation_points'] = expansion_points
                result['expansion_method'] = True

            return result

        except Exception as e:
            if self.debug:
                print(f"密度边界检测失败: {e}")
            return None

    def _initialize_ellipse_from_mean(self, x_coords, y_coords):
        """根据突变点的25%初始化椭圆"""
        try:
            if len(x_coords) < 10:
                return None

            # 计算距离
            distances = np.sqrt(x_coords ** 2 + y_coords ** 2)

            # 找到突变点（使用75%分位数作为突变点）
            mutation_distance = np.percentile(distances, 75)

            # 使用突变点的25%作为初始椭圆参数
            initial_a = mutation_distance * 0.25  # 船头方向
            initial_b = mutation_distance * 0.25  # 横向方向

            return {
                'a': initial_a,
                'b': initial_b,
                'mutation_distance': mutation_distance,
                'mean_distance': np.mean(distances),
                'std_distance': np.std(distances)
            }

        except Exception as e:
            if self.debug:
                print(f"椭圆初始化失败: {e}")
            return None

    def _expand_and_find_mutation_points(self, x_coords, y_coords, initial_ellipse):
        """逐步扩展找到突变点"""
        try:
            # 扩展步长
            expansion_step = 10.0  # 10米
            max_expansions = 50  # 最大扩展次数

            # 初始化扩展椭圆
            current_a = initial_ellipse['a']
            current_b = initial_ellipse['b']

            mutation_points = []

            for expansion in range(max_expansions):
                # 计算当前椭圆边界上的点密度
                boundary_density = self._calculate_boundary_density(x_coords, y_coords, current_a, current_b)

                # 检查密度突变
                if self._detect_density_mutation(boundary_density, expansion):
                    # 找到突变点，记录当前椭圆参数
                    mutation_points.append({
                        'expansion_step': expansion,
                        'a': current_a,
                        'b': current_b,
                        'density': boundary_density
                    })

                # 向外扩展
                current_a += expansion_step
                current_b += expansion_step

                # 如果扩展太远，停止
                if current_a > 2000 or current_b > 2000:
                    break

            return mutation_points

        except Exception as e:
            if self.debug:
                print(f"扩展检测失败: {e}")
            return None

    def _calculate_boundary_density(self, x_coords, y_coords, a, b):
        """计算椭圆边界附近的点密度"""
        try:
            # 计算每个点到椭圆边界的距离
            distances_to_boundary = []

            for x, y in zip(x_coords, y_coords):
                # 计算点到椭圆的距离
                distance = self._point_to_ellipse_distance(x, y, a, b)
                distances_to_boundary.append(distance)

            # 统计在边界附近的点（±20米范围内）
            boundary_tolerance = 20.0
            boundary_points = sum(1 for d in distances_to_boundary if abs(d) <= boundary_tolerance)

            # 计算密度（点数/周长）
            perimeter = 2 * np.pi * np.sqrt((a ** 2 + b ** 2) / 2)  # 椭圆周长近似
            density = boundary_points / perimeter if perimeter > 0 else 0

            return density

        except Exception as e:
            if self.debug:
                print(f"边界密度计算失败: {e}")
            return 0

    def _point_to_ellipse_distance(self, x, y, a, b):
        """计算点到椭圆的距离"""
        try:
            # 简化的点到椭圆距离计算
            # 使用椭圆的参数方程
            if a == 0 or b == 0:
                return float('inf')

            # 计算点到椭圆中心的距离
            distance_to_center = np.sqrt(x ** 2 + y ** 2)

            # 计算椭圆在该方向上的半径
            if distance_to_center == 0:
                return 0

            # 计算角度
            angle = np.arctan2(y, x)

            # 椭圆在该方向上的半径
            r_ellipse = (a * b) / np.sqrt((b * np.cos(angle)) ** 2 + (a * np.sin(angle)) ** 2)

            # 距离差
            distance_diff = distance_to_center - r_ellipse

            return distance_diff

        except Exception as e:
            return float('inf')

    def _detect_density_mutation(self, current_density, expansion_step):
        """检测密度突变"""
        try:
            # 简单的突变检测：密度突然下降
            # 这里可以根据需要调整阈值
            if expansion_step < 3:  # 前几步不检测
                return False

            # 如果密度很低，认为是突变点
            if current_density < 0.01:  # 密度阈值
                return True

            return False

        except Exception as e:
            return False

    def _fit_ellipse_from_mutation_points(self, mutation_points):
        """基于突变点拟合椭圆"""
        try:
            if len(mutation_points) < 3:
                return None

            # 提取突变点的椭圆参数
            a_values = [point['a'] for point in mutation_points]
            b_values = [point['b'] for point in mutation_points]

            # 使用突变点的平均值作为最终椭圆参数
            final_a = np.mean(a_values)
            final_b = np.mean(b_values)

            if final_a <= 0 or final_b <= 0:
                return None

            return {
                'a': float(final_a),
                'b': float(final_b),
                'data_count': len(mutation_points),
                'cleaned_count': len(mutation_points),
                'expansion_method': True
            }

        except Exception as e:
            if self.debug:
                print(f"基于突变点拟合椭圆失败: {e}")
            return None

    def _calculate_density_profile(self, distances):
        """计算密度分布轮廓"""
        try:
            # 使用直方图方法计算密度分布
            hist, bin_edges = np.histogram(distances, bins=50, density=True)
            bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2

            # 使用高斯滤波平滑密度分布
            from scipy.ndimage import gaussian_filter1d
            smoothed_density = gaussian_filter1d(hist, sigma=1.0)

            return {
                'distances': bin_centers,
                'density': smoothed_density,
                'raw_hist': hist,
                'bin_edges': bin_edges
            }

        except Exception as e:
            if self.debug:
                print(f"密度分布计算失败: {e}")
            return None

    def _detect_density_change_point(self, density_profile, all_distances):
        """检测密度突变点"""
        try:
            if density_profile is None:
                return None

            distances = density_profile['distances']
            density = density_profile['density']

            # 计算密度梯度
            density_gradient = np.gradient(density)

            # 寻找梯度变化最大的点（密度突变点）
            # 使用滑动窗口计算局部梯度变化
            window_size = 5
            gradient_changes = []

            for i in range(window_size, len(density_gradient) - window_size):
                # 计算局部梯度变化
                local_gradient = density_gradient[i - window_size:i + window_size]
                gradient_change = np.std(local_gradient)
                gradient_changes.append(gradient_change)

            # 找到梯度变化最大的点
            if len(gradient_changes) > 0:
                max_change_idx = np.argmax(gradient_changes) + window_size
                boundary_distance = distances[max_change_idx]

                # 确保边界距离在合理范围内
                if 50 <= boundary_distance <= 2000:
                    return boundary_distance

            # 如果没找到明显的突变点，使用百分位数方法
            boundary_distance = np.percentile(all_distances, 85)  # 使用85%分位数

            return boundary_distance

        except Exception as e:
            if self.debug:
                print(f"密度突变点检测失败: {e}")
            return None

    def _extract_boundary_points(self, x_coords, y_coords, boundary_distance):
        """提取边界点"""
        try:
            # 计算每个点到原点的距离
            distances = np.sqrt(x_coords ** 2 + y_coords ** 2)

            # 选择在边界距离附近的点（±10%范围）
            tolerance = boundary_distance * 0.1
            boundary_mask = (distances >= boundary_distance - tolerance) & (distances <= boundary_distance + tolerance)

            boundary_x = x_coords[boundary_mask]
            boundary_y = y_coords[boundary_mask]

            # 返回边界点坐标对
            boundary_points = list(zip(boundary_x, boundary_y))

            return boundary_points

        except Exception as e:
            if self.debug:
                print(f"边界点提取失败: {e}")
            return []

    def _fit_ellipse_traditional(self, x_coords, y_coords):
        """传统的椭圆拟合方法（作为回退方案）"""
        try:
            x_coords = np.array(x_coords)
            y_coords = np.array(y_coords)

            if len(x_coords) < 10:
                return None

            # 初始参数估计
            a_initial = (np.max(y_coords) - np.min(y_coords)) / 2
            b_initial = (np.max(x_coords) - np.min(x_coords)) / 2

            if a_initial <= 0 or b_initial <= 0:
                return None

            # 最小二乘拟合
            params, _ = leastsq(self._ellipse_equation, [a_initial, b_initial], args=(x_coords, y_coords))
            a, b = abs(params[0]), abs(params[1])

            if a <= 0 or b <= 0:
                return None

            return {
                'a': float(a),
                'b': float(b),
                'data_count': len(x_coords),
                'cleaned_count': len(x_coords),
                'density_method': False
            }

        except Exception as e:
            if self.debug:
                print(f"传统椭圆拟合失败: {e}")
            return None

    def _fit_ellipse_with_weighting(self, boundary_points):
        """等权重椭圆拟合"""
        try:
            if len(boundary_points) < 10:
                return None

            # 分离坐标
            boundary_x = np.array([p[0] for p in boundary_points])
            boundary_y = np.array([p[1] for p in boundary_points])

            # 使用传统等权重拟合
            result = self._fit_ellipse_traditional(boundary_x, boundary_y)

            if result is not None:
                result['equal_weight_method'] = True

            return result

        except Exception as e:
            if self.debug:
                print(f"等权重椭圆拟合失败: {e}")
            return None

    def _fit_scenario_ellipses(self, relative_data, ellipse_params, scenario_type):
        """为指定场景拟合椭圆"""
        for _, _, interval_name in self.length_intervals:
            data_points = relative_data[interval_name]

            if len(data_points) < 20:  # 降低数据要求
                print(f"   {scenario_type}-{interval_name}: 数据点不足({len(data_points)}个)，跳过")
                ellipse_params[interval_name] = None
                continue

            # 提取坐标
            x_coords = [p['relative_x'] for p in data_points]
            y_coords = [p['relative_y'] for p in data_points]

            # 拟合椭圆
            result = self._fit_ellipse(x_coords, y_coords)
            ellipse_params[interval_name] = result

            if result is not None:
                print(
                    f"   {scenario_type}-{interval_name}: a={result['a']:.0f}m, b={result['b']:.0f}m, 数据{len(data_points)}个")
            else:
                print(f"   {scenario_type}-{interval_name}: 拟合失败")

    @staticmethod
    def _ellipse_equation(params, x, y):
        """椭圆方程"""
        a, b = params
        return (x / b) ** 2 + (y / a) ** 2 - 1

    def save_results(self):
        """保存结果"""
        print("\n=== 保存结果 ===")

        result_dir = Path("result/simplified_fitting")
        result_dir.mkdir(exist_ok=True)

        # 保存主结果
        results = {
            'crossing_ellipse_params': self.crossing_ellipse_params,
            'overtaking_ellipse_params': self.overtaking_ellipse_params,
            'length_intervals': self.length_intervals,
            'data_months': self.data_months
        }

        result_file = result_dir / "ship_domain_fitting_results.pkl"
        with open(result_file, 'wb') as f:
            pickle.dump(results, f)

        # 保存CSV
        self._save_csv_results(result_dir)

        print(f"✅ 结果保存完成: {result_file}")

    def _save_csv_results(self, result_dir):
        """保存CSV结果"""
        data = []

        for _, _, interval_name in self.length_intervals:
            # 交叉避让
            params = self.crossing_ellipse_params.get(interval_name)
            if params is not None:
                data.append({
                    'scenario_type': '交叉避让',
                    'length_interval': interval_name,
                    'a_longitudinal_m': round(params['a'], 1),
                    'b_lateral_m': round(params['b'], 1),
                    'data_count': params.get('data_count', 0),
                    'boundary_count': len(params.get('boundary_points', []))
                })

            # 追越避让
            params = self.overtaking_ellipse_params.get(interval_name)
            if params is not None:
                data.append({
                    'scenario_type': '追越避让',
                    'length_interval': interval_name,
                    'a_longitudinal_m': round(params['a'], 1),
                    'b_lateral_m': round(params['b'], 1),
                    'data_count': params.get('data_count', 0),
                    'boundary_count': len(params.get('boundary_points', []))
                })

        if data:
            df = pd.DataFrame(data)
            csv_file = result_dir / "ellipse_parameters.csv"
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"   CSV文件: {csv_file}")

    def run_full_pipeline(self):
        """运行完整流程"""
        print("🚢 开始简化船舶领域拟合流程...")
        print("=" * 60)

        start_time = time.time()

        # 执行流程
        self.load_maneuvering_moments()
        self.extract_relative_positions()
        self.fit_ellipses()
        self.save_results()

        total_time = time.time() - start_time

        print("\n" + "=" * 60)
        print("🎉 简化船舶领域拟合完成！")
        print(f"⚡ 总耗时: {total_time:.2f}秒")
        print("=" * 60)

        return True


def main():
    """主函数"""
    data_months = ['2024_1', '2024_2', '2024_3']

    print(f"📊 简化版船舶领域拟合")
    print(f"   数据月份: {data_months}")
    print(f"   输入文件: crossing_avoidance_scenes.pkl, overtaking_avoidance_scenes.pkl")

    # 创建拟合器
    fitter = SimplifiedShipDomainFitting(data_months=data_months, debug=False)

    # 运行完整流程
    success = fitter.run_full_pipeline()

    if success:
        print(f"\n📁 输出文件:")
        print(f"   主结果: result/simplified_fitting/ship_domain_fitting_results.pkl")
        print(f"   椭圆参数: result/simplified_fitting/ellipse_parameters.csv")
    else:
        print(f"\n❌ 拟合流程失败")


if __name__ == '__main__':
    main()
