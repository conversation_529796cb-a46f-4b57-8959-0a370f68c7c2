"""
检查现有结果文件中的船长分类
确认是否需要重新生成结果
"""

import pickle
from pathlib import Path

def check_probability_density_results():
    """检查概率密度分析结果"""
    print("🔍 检查现有的概率密度分析结果")
    print("=" * 50)
    
    result_file = Path("result/probability_density/sector_boundaries_results.pkl")
    
    if not result_file.exists():
        print("❌ 未找到概率密度分析结果文件")
        print(f"   文件路径: {result_file}")
        print("   需要运行 probability_density_analysis.py 生成结果")
        return False
    
    try:
        with open(result_file, 'rb') as f:
            results = pickle.load(f)
        
        print(f"✅ 找到结果文件: {result_file}")
        
        # 检查船长区间
        if 'length_intervals' in results:
            length_intervals = results['length_intervals']
            print(f"\n📊 当前船长区间分类:")
            for min_len, max_len, interval_name in length_intervals:
                print(f"   {interval_name}: [{min_len}, {max_len}) 米")
            
            # 判断是否为新分类
            interval_names = [name for _, _, name in length_intervals]
            
            expected_new_names = ["100m以下", "100-200m", "200m以上"]
            old_names = ["0-50m", "50-100m", "100-150m", "150-200m", "200-250m"]
            
            if set(interval_names) == set(expected_new_names):
                print(f"\n✅ 使用新的3区间分类 - 无需更新")
                return True
            elif any(name in old_names for name in interval_names):
                print(f"\n⚠️  使用旧的5区间分类 - 需要重新生成")
                print(f"   当前: {interval_names}")
                print(f"   期望: {expected_new_names}")
                return False
            else:
                print(f"\n❓ 未知的分类格式: {interval_names}")
                return False
        else:
            print(f"\n❌ 结果文件中缺少 length_intervals 字段")
            return False
            
    except Exception as e:
        print(f"❌ 读取结果文件失败: {e}")
        return False

def check_ship_domain_fitting_results():
    """检查船舶领域拟合结果"""
    print(f"\n🔍 检查现有的船舶领域拟合结果")
    print("=" * 50)
    
    result_file = Path("result/ship_domain_fitting/ship_domain_ellipse_params.pkl")
    
    if not result_file.exists():
        print("❌ 未找到船舶领域拟合结果文件")
        print(f"   文件路径: {result_file}")
        print("   需要运行 ship_domain_fitting.py 生成结果")
        return False
    
    try:
        with open(result_file, 'rb') as f:
            results = pickle.load(f)
        
        print(f"✅ 找到结果文件: {result_file}")
        
        # 检查船长区间
        if 'length_intervals' in results:
            length_intervals = results['length_intervals']
            print(f"\n📊 当前船长区间分类:")
            for min_len, max_len, interval_name in length_intervals:
                print(f"   {interval_name}: [{min_len}, {max_len}) 米")
        
        # 检查椭圆参数
        if 'all_ellipse_params' in results:
            all_params = results['all_ellipse_params']
            print(f"\n📈 椭圆参数统计:")
            print(f"   总场景数: {len(all_params)}")
            
            # 按场景类型分组
            crossing_count = 0
            overtaking_count = 0
            
            for key in all_params.keys():
                if key.startswith("交叉"):
                    crossing_count += 1
                elif key.startswith("追越"):
                    overtaking_count += 1
            
            print(f"   交叉避让: {crossing_count} 个场景")
            print(f"   追越避让: {overtaking_count} 个场景")
            
            # 列出所有场景
            print(f"\n📋 所有场景列表:")
            for key in sorted(all_params.keys()):
                params = all_params[key]
                print(f"   {key}: a={params['a']:.1f}m, b={params['b']:.1f}m")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取结果文件失败: {e}")
        return False

def check_csv_results():
    """检查CSV结果文件"""
    print(f"\n🔍 检查CSV结果文件")
    print("=" * 50)
    
    csv_files = [
        "result/ship_domain_fitting/ship_domain_ellipse_parameters.csv",
        "result/domain_fitting/ellipse_parameters.csv",
        "result/simplified_fitting/ellipse_parameters.csv"
    ]
    
    for csv_file in csv_files:
        csv_path = Path(csv_file)
        if csv_path.exists():
            print(f"✅ 找到CSV文件: {csv_path}")
            
            try:
                import pandas as pd
                df = pd.read_csv(csv_path)
                
                print(f"   记录数: {len(df)}")
                if 'length_interval' in df.columns:
                    intervals = df['length_interval'].unique()
                    print(f"   船长区间: {list(intervals)}")
                elif 'scenario_type' in df.columns:
                    scenarios = df['scenario_type'].unique()
                    print(f"   场景类型: {list(scenarios)}")
                
            except Exception as e:
                print(f"   ❌ 读取CSV失败: {e}")
        else:
            print(f"❌ 未找到CSV文件: {csv_path}")

def generate_cleanup_recommendations():
    """生成清理建议"""
    print(f"\n🧹 清理建议")
    print("=" * 50)
    
    print("如果发现使用旧分类，建议执行以下步骤:")
    print("1. 删除旧的结果文件:")
    print("   - result/probability_density/sector_boundaries_results.pkl")
    print("   - result/ship_domain_fitting/ship_domain_ellipse_params.pkl")
    print("   - result/ship_domain_fitting/ship_domain_ellipse_parameters.csv")
    
    print(f"\n2. 重新运行分析:")
    print("   - python probability_density_analysis.py")
    print("   - python ship_domain_fitting.py")
    
    print(f"\n3. 验证新结果:")
    print("   - 检查是否包含 '100m以下', '100-200m', '200m以上' 三个区间")
    print("   - 确认200米以上船舶被正确分类")

def main():
    """主函数"""
    print("🔍 现有结果文件检查")
    print("=" * 60)
    
    # 检查各个结果文件
    prob_density_ok = check_probability_density_results()
    ship_fitting_ok = check_ship_domain_fitting_results()
    check_csv_results()
    
    # 生成建议
    generate_cleanup_recommendations()
    
    print(f"\n" + "=" * 60)
    if prob_density_ok and ship_fitting_ok:
        print("🎉 所有结果文件都使用新的船长分类")
    else:
        print("⚠️  部分结果文件使用旧分类，建议重新生成")
        
        # 提供清理命令
        print(f"\n🔧 快速清理命令:")
        print("import os")
        print("os.remove('result/probability_density/sector_boundaries_results.pkl')")
        print("os.remove('result/ship_domain_fitting/ship_domain_ellipse_params.pkl')")

if __name__ == '__main__':
    main()
