"""
船舶领域可视化系统
基于概率密度分析和椭圆拟合结果，生成完整的可视化图表

可视化步骤：
1. 船舶分布 + 最远距离圆 + 坐标轴直径
2. 扇区划分
3. 各扇区密度随距离变化
4. 扇区边界标注
5. 船舶领域椭圆

使用方法：
python ship_domain_visualization.py
"""

import pickle
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd
from scipy import stats
from scipy.ndimage import gaussian_filter1d

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.facecolor'] = 'white'


class ShipDomainVisualizer:
    """船舶领域可视化器"""
    
    def __init__(self, debug=False):
        """初始化可视化器"""
        self.debug = debug
        
        # 数据存储
        self.density_results = {}
        self.sector_boundaries = {}
        self.ellipse_params = {}
        self.length_intervals = []
        
        # 可视化配置
        self.colors = {
            'ship_points': '#1f77b4',      # 蓝色点
            'max_circle': 'black',         # 黑色实线
            'axis_lines': 'red',           # 红色虚线
            'sector_lines': 'black',       # 黑色虚线
            'boundary_points': 'red',      # 红色边界点
            'ellipse': 'hotpink'           # 粉色椭圆
        }
        
        print("🎨 船舶领域可视化器初始化完成")
    
    def load_data(self):
        """加载所有必要的数据"""
        print("\n=== 加载数据 ===")
        
        # 1. 加载概率密度分析结果
        density_file = Path("result/probability_density/sector_boundaries_results.pkl")
        if not density_file.exists():
            print(f"❌ 未找到概率密度分析结果: {density_file}")
            return False
        
        with open(density_file, 'rb') as f:
            density_data = pickle.load(f)
        
        self.density_results = density_data['density_results']
        self.sector_boundaries = density_data['sector_boundaries']
        self.length_intervals = density_data['length_intervals']
        
        print(f"✅ 加载概率密度结果: {len(self.density_results)} 个场景")
        
        # 2. 加载椭圆拟合结果
        ellipse_file = Path("result/ship_domain_fitting/ship_domain_ellipse_params.pkl")
        if not ellipse_file.exists():
            print(f"❌ 未找到椭圆拟合结果: {ellipse_file}")
            return False
        
        with open(ellipse_file, 'rb') as f:
            ellipse_data = pickle.load(f)
        
        self.ellipse_params = ellipse_data['all_ellipse_params']
        
        print(f"✅ 加载椭圆参数: {len(self.ellipse_params)} 个场景")
        
        return True
    
    def create_comprehensive_visualization(self):
        """创建完整的可视化图表"""
        print("\n=== 生成完整可视化 ===")
        
        vis_dir = Path("vis/comprehensive_visualization")
        vis_dir.mkdir(parents=True, exist_ok=True)
        
        # 为每个场景生成可视化
        for key in self.density_results.keys():
            if key in self.sector_boundaries and key in self.ellipse_params:
                print(f"📊 生成 {key} 的可视化...")
                self._create_scene_visualization(key, vis_dir)
        
        print(f"✅ 可视化完成，保存至: {vis_dir}")
    
    def _create_scene_visualization(self, scene_key, vis_dir):
        """为单个场景创建5步可视化"""
        # 获取数据
        density_data = self.density_results[scene_key]
        boundaries = self.sector_boundaries.get(scene_key, {})
        ellipse = self.ellipse_params.get(scene_key, {})
        
        x_coords = density_data['x_coords']
        y_coords = density_data['y_coords']
        sector_analysis = density_data['sector_analysis']
        
        # 创建5个子图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'{scene_key.replace("_", " - ")} 船舶领域分析', fontsize=16, fontweight='bold')
        
        # 步骤1: 船舶分布 + 最远距离圆 + 坐标轴
        self._plot_step1_ship_distribution(axes[0, 0], x_coords, y_coords)
        
        # 步骤2: 扇区划分
        self._plot_step2_sector_division(axes[0, 1], x_coords, y_coords, sector_analysis)
        
        # 步骤3: 密度随距离变化
        self._plot_step3_density_distance(axes[0, 2], sector_analysis)
        
        # 步骤4: 扇区边界
        self._plot_step4_sector_boundaries(axes[1, 0], x_coords, y_coords, boundaries, sector_analysis)
        
        # 步骤5: 船舶领域椭圆
        self._plot_step5_ship_domain(axes[1, 1], x_coords, y_coords, boundaries, ellipse, sector_analysis)
        
        # 隐藏第6个子图
        axes[1, 2].axis('off')
        
        plt.tight_layout()
        
        # 保存图片
        scenario_type, length_interval = scene_key.split('_', 1)
        filename = f"{scenario_type}_{length_interval}_comprehensive_analysis.png"
        save_path = vis_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"   保存至: {save_path}")
    
    def _plot_step1_ship_distribution(self, ax, x_coords, y_coords):
        """步骤1: 船舶分布 + 最远距离圆 + 坐标轴直径"""
        # 绘制船舶分布（蓝色点）
        ax.scatter(x_coords, y_coords, c=self.colors['ship_points'], s=2, alpha=0.6)
        
        # 计算最远距离
        distances = np.sqrt(x_coords**2 + y_coords**2)
        max_distance = np.max(distances)
        
        # 绘制最远距离圆（黑色实线）
        circle = plt.Circle((0, 0), max_distance, fill=False, 
                           color=self.colors['max_circle'], linewidth=2)
        ax.add_patch(circle)
        
        # 绘制坐标轴直径（红色虚线）
        ax.plot([-max_distance, max_distance], [0, 0], 
                color=self.colors['axis_lines'], linestyle='--', linewidth=2)
        ax.plot([0, 0], [-max_distance, max_distance], 
                color=self.colors['axis_lines'], linestyle='--', linewidth=2)
        
        # 标记本船位置
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow', markeredgewidth=2)
        
        ax.set_xlim(-max_distance*1.1, max_distance*1.1)
        ax.set_ylim(-max_distance*1.1, max_distance*1.1)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('横向距离 (m)')
        ax.set_ylabel('纵向距离 (m)')
    
    def _plot_step2_sector_division(self, ax, x_coords, y_coords, sector_analysis):
        """步骤2: 扇区划分"""
        # 绘制船舶分布
        ax.scatter(x_coords, y_coords, c=self.colors['ship_points'], s=2, alpha=0.6)
        
        # 计算最远距离用于扇区线长度
        distances = np.sqrt(x_coords**2 + y_coords**2)
        max_distance = np.max(distances)
        
        # 绘制扇区分割线（黑色虚线）
        num_sectors = 8
        for i in range(num_sectors):
            angle = i * 2 * np.pi / num_sectors - np.pi
            x_end = max_distance * 1.1 * np.cos(angle)
            y_end = max_distance * 1.1 * np.sin(angle)
            
            ax.plot([0, x_end], [0, y_end], 
                   color=self.colors['sector_lines'], linestyle='--', linewidth=1, alpha=0.7)
        
        # 标记本船位置
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow', markeredgewidth=2)
        
        ax.set_xlim(-max_distance*1.1, max_distance*1.1)
        ax.set_ylim(-max_distance*1.1, max_distance*1.1)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('横向距离 (m)')
        ax.set_ylabel('纵向距离 (m)')
    
    def _plot_step3_density_distance(self, ax, sector_analysis):
        """步骤3: 各扇区密度随距离变化"""
        colors = plt.cm.Set3(np.linspace(0, 1, 8))
        
        for i, (sector_name, sector_data) in enumerate(sector_analysis.items()):
            if sector_data is not None and len(sector_data['x_coords']) > 5:
                # 计算该扇区的距离分布
                sector_x = sector_data['x_coords']
                sector_y = sector_data['y_coords']
                distances = np.sqrt(sector_x**2 + sector_y**2)
                
                # 创建距离区间并统计密度
                max_dist = np.max(distances)
                distance_bins = np.linspace(0, max_dist, 20)
                density, _ = np.histogram(distances, bins=distance_bins, density=True)
                bin_centers = (distance_bins[:-1] + distance_bins[1:]) / 2
                
                # 平滑密度曲线
                if len(density) > 3:
                    smoothed_density = gaussian_filter1d(density, sigma=1.0)
                    ax.plot(bin_centers, smoothed_density, 
                           color=colors[i], linewidth=2, alpha=0.8, 
                           label=f'{sector_name}({len(distances)}点)')
        
        ax.set_xlabel('距离 (m)')
        ax.set_ylabel('密度')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
        ax.grid(True, alpha=0.3)

    def _plot_step4_sector_boundaries(self, ax, x_coords, y_coords, boundaries, sector_analysis):
        """步骤4: 扇区边界标注"""
        # 绘制船舶分布
        ax.scatter(x_coords, y_coords, c=self.colors['ship_points'], s=2, alpha=0.4)

        # 计算最远距离
        distances = np.sqrt(x_coords**2 + y_coords**2)
        max_distance = np.max(distances)

        # 绘制扇区分割线
        num_sectors = 8
        for i in range(num_sectors):
            angle = i * 2 * np.pi / num_sectors - np.pi
            x_end = max_distance * 1.1 * np.cos(angle)
            y_end = max_distance * 1.1 * np.sin(angle)

            ax.plot([0, x_end], [0, y_end],
                   color=self.colors['sector_lines'], linestyle='--', linewidth=1, alpha=0.5)

        # 绘制扇区边界点和距离标注
        for sector_name, boundary_info in boundaries.items():
            boundary_distance = boundary_info['boundary_distance']
            boundary_angle = boundary_info['boundary_angle']

            # 边界点坐标
            bx = boundary_distance * np.cos(boundary_angle)
            by = boundary_distance * np.sin(boundary_angle)

            # 绘制边界点
            ax.plot(bx, by, 'o', color=self.colors['boundary_points'],
                   markersize=8, markeredgewidth=2, markerfacecolor='white')

            # 标注距离
            text_x = bx * 1.15
            text_y = by * 1.15
            ax.text(text_x, text_y, f'{boundary_distance:.0f}m',
                   ha='center', va='center', fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))

        # 标记本船位置
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow', markeredgewidth=2)

        ax.set_xlim(-max_distance*1.3, max_distance*1.3)
        ax.set_ylim(-max_distance*1.3, max_distance*1.3)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('横向距离 (m)')
        ax.set_ylabel('纵向距离 (m)')

    def _plot_step5_ship_domain(self, ax, x_coords, y_coords, boundaries, ellipse, sector_analysis):
        """步骤5: 船舶领域椭圆"""
        # 绘制船舶分布（更淡）
        ax.scatter(x_coords, y_coords, c=self.colors['ship_points'], s=2, alpha=0.3)

        # 计算最远距离
        distances = np.sqrt(x_coords**2 + y_coords**2)
        max_distance = np.max(distances)

        # 绘制扇区分割线（更淡）
        num_sectors = 8
        for i in range(num_sectors):
            angle = i * 2 * np.pi / num_sectors - np.pi
            x_end = max_distance * 1.1 * np.cos(angle)
            y_end = max_distance * 1.1 * np.sin(angle)

            ax.plot([0, x_end], [0, y_end],
                   color=self.colors['sector_lines'], linestyle='--', linewidth=1, alpha=0.3)

        # 绘制扇区边界点（更小）
        for sector_name, boundary_info in boundaries.items():
            boundary_distance = boundary_info['boundary_distance']
            boundary_angle = boundary_info['boundary_angle']

            bx = boundary_distance * np.cos(boundary_angle)
            by = boundary_distance * np.sin(boundary_angle)

            ax.plot(bx, by, 'o', color=self.colors['boundary_points'],
                   markersize=6, alpha=0.7)

        # 绘制船舶领域椭圆（粉色实线）
        if ellipse and 'a' in ellipse and 'b' in ellipse:
            a, b = ellipse['a'], ellipse['b']

            # 生成椭圆点
            theta = np.linspace(0, 2*np.pi, 100)
            ellipse_x = b * np.cos(theta)  # 横向半轴
            ellipse_y = a * np.sin(theta)  # 纵向半轴

            ax.plot(ellipse_x, ellipse_y, color=self.colors['ellipse'],
                   linewidth=3, label=f'船舶领域 (a={a:.0f}m, b={b:.0f}m)')

            # 标注椭圆参数
            ax.text(0.02, 0.98, f'长半轴: {a:.0f}m\n短半轴: {b:.0f}m\n长短比: {a/b:.2f}',
                   transform=ax.transAxes, va='top', ha='left',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='lightpink', alpha=0.8))

        # 标记本船位置
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow', markeredgewidth=2)

        ax.set_xlim(-max_distance*1.1, max_distance*1.1)
        ax.set_ylim(-max_distance*1.1, max_distance*1.1)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('横向距离 (m)')
        ax.set_ylabel('纵向距离 (m)')

    def create_summary_comparison(self):
        """创建不同场景和船长的对比图"""
        print("\n=== 生成对比分析 ===")

        vis_dir = Path("vis/comprehensive_visualization")
        vis_dir.mkdir(parents=True, exist_ok=True)

        # 按场景类型分组
        crossing_scenes = {k: v for k, v in self.ellipse_params.items() if k.startswith('交叉')}
        overtaking_scenes = {k: v for k, v in self.ellipse_params.items() if k.startswith('追越')}

        if crossing_scenes:
            self._create_scenario_comparison('交叉避让', crossing_scenes, vis_dir)

        if overtaking_scenes:
            self._create_scenario_comparison('追越避让', overtaking_scenes, vis_dir)

    def _create_scenario_comparison(self, scenario_name, scenes, vis_dir):
        """创建单个场景类型的对比图"""
        n_scenes = len(scenes)
        if n_scenes == 0:
            return

        # 计算子图布局
        cols = min(3, n_scenes)
        rows = (n_scenes + cols - 1) // cols

        fig, axes = plt.subplots(rows, cols, figsize=(6*cols, 6*rows))
        if n_scenes == 1:
            axes = [axes]
        elif rows == 1:
            axes = [axes]
        else:
            axes = axes.flatten()

        fig.suptitle(f'{scenario_name}不同船长的船舶领域对比', fontsize=16, fontweight='bold')

        for i, (scene_key, ellipse) in enumerate(scenes.items()):
            ax = axes[i] if n_scenes > 1 else axes[0]

            # 获取数据
            if scene_key in self.density_results:
                density_data = self.density_results[scene_key]
                x_coords = density_data['x_coords']
                y_coords = density_data['y_coords']

                # 绘制数据点
                ax.scatter(x_coords, y_coords, c=self.colors['ship_points'], s=1, alpha=0.4)

            # 绘制椭圆
            if 'a' in ellipse and 'b' in ellipse:
                a, b = ellipse['a'], ellipse['b']

                theta = np.linspace(0, 2*np.pi, 100)
                ellipse_x = b * np.cos(theta)
                ellipse_y = a * np.sin(theta)

                ax.plot(ellipse_x, ellipse_y, color=self.colors['ellipse'], linewidth=3)

                # 标注
                _, length_interval = scene_key.split('_', 1)
                ax.set_title(f'{length_interval}\na={a:.0f}m, b={b:.0f}m')

            # 标记本船
            ax.plot(0, 0, 'ko', markersize=6, markerfacecolor='yellow')

            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            ax.set_xlabel('横向距离 (m)')
            ax.set_ylabel('纵向距离 (m)')

        # 隐藏多余的子图
        for i in range(n_scenes, len(axes)):
            axes[i].axis('off')

        plt.tight_layout()

        # 保存
        filename = f"{scenario_name}_comparison.png"
        save_path = vis_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 {scenario_name}对比图保存至: {save_path}")

    def run_full_visualization(self):
        """运行完整的可视化流程"""
        print("🎨 开始船舶领域可视化...")
        print("=" * 60)

        try:
            # 加载数据
            if not self.load_data():
                return False

            # 生成完整可视化
            self.create_comprehensive_visualization()

            # 生成对比分析
            self.create_summary_comparison()

            print("\n" + "=" * 60)
            print("🎉 船舶领域可视化完成！")
            print("📁 输出目录: vis/comprehensive_visualization/")
            print("=" * 60)

            return True

        except Exception as e:
            print(f"\n❌ 可视化失败: {e}")
            if self.debug:
                import traceback
                traceback.print_exc()
            return False


def main():
    """主函数"""
    print("🎨 船舶领域可视化系统")
    print("输入: sector_boundaries_results.pkl, ship_domain_ellipse_params.pkl")
    print("输出: 完整的5步可视化图表")

    # 创建可视化器
    visualizer = ShipDomainVisualizer(debug=False)

    # 运行完整可视化
    success = visualizer.run_full_visualization()

    if success:
        print("\n📊 主要输出:")
        print("   • 各场景完整分析图: *_comprehensive_analysis.png")
        print("   • 场景对比图: *_comparison.png")
        print("   • 可视化步骤:")
        print("     1. 船舶分布 + 最远距离圆 + 坐标轴")
        print("     2. 扇区划分")
        print("     3. 密度随距离变化")
        print("     4. 扇区边界标注")
        print("     5. 船舶领域椭圆")
    else:
        print("\n❌ 可视化失败")


if __name__ == '__main__':
    main()
