"""
船舶领域可视化系统
基于概率密度分析和椭圆拟合结果，生成完整的可视化图表

可视化步骤：
1. 船舶分布 + 最远距离圆 + 坐标轴直径
2. 扇区划分
3. 各扇区密度随距离变化
4. 扇区边界标注
5. 船舶领域椭圆

使用方法：
直接修改下面的控制参数，然后运行即可
"""

# ==================== 控制参数 ====================
# 修改这些参数来控制要生成的可视化

# 要可视化的场景（None表示所有场景）
TARGET_SCENE = "交叉_100m以下"  # 例如: "交叉_100m以下", "追越_100-200m", None

# 要生成的步骤（None表示生成综合图，1-5表示单独步骤）
TARGET_STEP = None  # 例如: None, 1, 2, 3, 4, 5

# 是否生成所有场景的完整可视化
GENERATE_ALL = False  # True表示生成所有场景，False表示只生成指定场景

# 是否生成对比图
GENERATE_COMPARISON = True  # True表示生成不同船长的对比图

# 调试模式
DEBUG_MODE = False

# ================================================

import pickle
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd
from scipy import stats
from scipy.ndimage import gaussian_filter1d

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.facecolor'] = 'white'


class ShipDomainVisualizer:
    """船舶领域可视化器"""
    
    def __init__(self, debug=False):
        """初始化可视化器"""
        self.debug = debug
        
        # 数据存储
        self.density_results = {}
        self.sector_boundaries = {}
        self.ellipse_params = {}
        self.length_intervals = []
        
        # 可视化配置
        self.colors = {
            'ship_points': '#1f77b4',      # 蓝色点
            'max_circle': 'black',         # 黑色实线
            'axis_lines': 'red',           # 红色虚线
            'sector_lines': 'black',       # 黑色虚线
            'boundary_points': 'red',      # 红色边界点
            'ellipse': 'hotpink'           # 粉色椭圆
        }
        
        print("🎨 船舶领域可视化器初始化完成")
    
    def load_data(self):
        """加载所有必要的数据"""
        print("\n=== 加载数据 ===")
        
        # 1. 加载概率密度分析结果
        density_file = Path("result/probability_density/sector_boundaries_results.pkl")
        if not density_file.exists():
            print(f"❌ 未找到概率密度分析结果: {density_file}")
            return False
        
        with open(density_file, 'rb') as f:
            density_data = pickle.load(f)
        
        self.density_results = density_data['density_results']
        self.sector_boundaries = density_data['sector_boundaries']
        self.length_intervals = density_data['length_intervals']
        
        print(f"✅ 加载概率密度结果: {len(self.density_results)} 个场景")
        
        # 2. 加载椭圆拟合结果
        ellipse_file = Path("result/ship_domain_fitting/ship_domain_ellipse_params.pkl")
        if not ellipse_file.exists():
            print(f"❌ 未找到椭圆拟合结果: {ellipse_file}")
            return False
        
        with open(ellipse_file, 'rb') as f:
            ellipse_data = pickle.load(f)
        
        self.ellipse_params = ellipse_data['all_ellipse_params']
        
        print(f"✅ 加载椭圆参数: {len(self.ellipse_params)} 个场景")
        
        return True
    
    def create_comprehensive_visualization(self):
        """创建完整的可视化图表"""
        print("\n=== 生成完整可视化 ===")
        
        vis_dir = Path("vis/comprehensive_visualization")
        vis_dir.mkdir(parents=True, exist_ok=True)
        
        # 为每个场景生成可视化
        for key in self.density_results.keys():
            if key in self.sector_boundaries and key in self.ellipse_params:
                print(f"📊 生成 {key} 的可视化...")
                self._create_scene_visualization(key, vis_dir)
        
        print(f"✅ 可视化完成，保存至: {vis_dir}")
    
    def _create_scene_visualization(self, scene_key, vis_dir):
        """为单个场景创建5步可视化"""
        # 获取数据
        density_data = self.density_results[scene_key]
        boundaries = self.sector_boundaries.get(scene_key, {})
        ellipse = self.ellipse_params.get(scene_key, {})
        
        x_coords = density_data['x_coords']
        y_coords = density_data['y_coords']
        sector_analysis = density_data['sector_analysis']
        
        # 创建5个子图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'{scene_key.replace("_", " - ")} 船舶领域分析', fontsize=16, fontweight='bold')
        
        # 步骤1: 船舶分布 + 最远距离圆 + 坐标轴
        self._plot_step1_ship_distribution(axes[0, 0], x_coords, y_coords)
        
        # 步骤2: 扇区划分
        self._plot_step2_sector_division(axes[0, 1], x_coords, y_coords, sector_analysis)
        
        # 步骤3: 密度随距离变化
        self._plot_step3_density_distance(axes[0, 2], sector_analysis)
        
        # 步骤4: 扇区边界
        self._plot_step4_sector_boundaries(axes[1, 0], x_coords, y_coords, boundaries, sector_analysis)
        
        # 步骤5: 船舶领域椭圆
        self._plot_step5_ship_domain(axes[1, 1], x_coords, y_coords, boundaries, ellipse, sector_analysis)
        
        # 隐藏第6个子图
        axes[1, 2].axis('off')
        
        plt.tight_layout()
        
        # 保存图片
        scenario_type, length_interval = scene_key.split('_', 1)
        filename = f"{scenario_type}_{length_interval}_comprehensive_analysis.png"
        save_path = vis_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"   保存至: {save_path}")
    
    def _plot_step1_ship_distribution(self, ax, x_coords, y_coords):
        """步骤1: 船舶分布 + 最远距离圆 + 坐标轴直径"""
        # 绘制船舶分布（蓝色点）
        ax.scatter(x_coords, y_coords, c=self.colors['ship_points'], s=2, alpha=0.6)
        
        # 计算最远距离
        distances = np.sqrt(x_coords**2 + y_coords**2)
        max_distance = np.max(distances)
        
        # 绘制最远距离圆（黑色实线）
        circle = plt.Circle((0, 0), max_distance, fill=False, 
                           color=self.colors['max_circle'], linewidth=2)
        ax.add_patch(circle)
        
        # 绘制坐标轴直径（红色虚线）
        ax.plot([-max_distance, max_distance], [0, 0], 
                color=self.colors['axis_lines'], linestyle='--', linewidth=2)
        ax.plot([0, 0], [-max_distance, max_distance], 
                color=self.colors['axis_lines'], linestyle='--', linewidth=2)
        
        # 标记本船位置
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow', markeredgewidth=2)
        
        ax.set_xlim(-max_distance*1.1, max_distance*1.1)
        ax.set_ylim(-max_distance*1.1, max_distance*1.1)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('横向距离 (m)')
        ax.set_ylabel('纵向距离 (m)')
    
    def _plot_step2_sector_division(self, ax, x_coords, y_coords, sector_analysis):
        """步骤2: 扇区划分"""
        # 绘制船舶分布
        ax.scatter(x_coords, y_coords, c=self.colors['ship_points'], s=2, alpha=0.6)

        # 计算最远距离用于扇区线长度
        distances = np.sqrt(x_coords**2 + y_coords**2)
        max_distance = np.max(distances)

        # 从扇区分析中获取实际的扇区角度范围
        sector_angles = []
        for sector_name, sector_data in sector_analysis.items():
            if sector_data is not None and 'angle_range' in sector_data:
                start_angle, end_angle = sector_data['angle_range']
                sector_angles.append(start_angle)

        # 如果没有角度信息，使用默认的8扇区划分
        if not sector_angles:
            num_sectors = 8
            sector_angles = [i * 2 * np.pi / num_sectors - np.pi for i in range(num_sectors)]
        else:
            # 去重并排序
            sector_angles = sorted(list(set(sector_angles)))

        # 绘制扇区分割线（黑色虚线）
        for angle in sector_angles:
            x_end = max_distance * 1.1 * np.cos(angle)
            y_end = max_distance * 1.1 * np.sin(angle)

            ax.plot([0, x_end], [0, y_end],
                   color=self.colors['sector_lines'], linestyle='--', linewidth=1, alpha=0.7)

        # 标记本船位置
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow', markeredgewidth=2)

        # 添加扇区数量标注
        ax.text(0.02, 0.02, f'扇区数: {len(sector_angles)}', transform=ax.transAxes,
               bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        ax.set_xlim(-max_distance*1.1, max_distance*1.1)
        ax.set_ylim(-max_distance*1.1, max_distance*1.1)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('横向距离 (m)')
        ax.set_ylabel('纵向距离 (m)')
    
    def _plot_step3_density_distance(self, ax, sector_analysis):
        """步骤3: 各扇区密度随距离变化"""
        # 动态生成足够的颜色
        num_sectors = len([s for s in sector_analysis.values() if s is not None])
        colors = plt.cm.tab20(np.linspace(0, 1, max(num_sectors, 20)))  # 至少20种颜色

        color_idx = 0
        for sector_name, sector_data in sector_analysis.items():
            if sector_data is not None and len(sector_data['x_coords']) > 5:
                # 计算该扇区的距离分布
                sector_x = sector_data['x_coords']
                sector_y = sector_data['y_coords']
                distances = np.sqrt(sector_x**2 + sector_y**2)

                # 创建距离区间并统计密度
                max_dist = np.max(distances)
                distance_bins = np.linspace(0, max_dist, 20)
                density, _ = np.histogram(distances, bins=distance_bins, density=True)
                bin_centers = (distance_bins[:-1] + distance_bins[1:]) / 2

                # 平滑密度曲线
                if len(density) > 3:
                    smoothed_density = gaussian_filter1d(density, sigma=1.0)
                    ax.plot(bin_centers, smoothed_density,
                           color=colors[color_idx % len(colors)], linewidth=2, alpha=0.8,
                           label=f'{sector_name}({len(distances)}点)')
                    color_idx += 1

        ax.set_xlabel('距离 (m)')
        ax.set_ylabel('密度')
        # 如果扇区太多，不显示图例
        if num_sectors <= 12:
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
        else:
            ax.text(0.02, 0.98, f'共{num_sectors}个扇区', transform=ax.transAxes,
                   va='top', ha='left', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        ax.grid(True, alpha=0.3)

    def _plot_step4_sector_boundaries(self, ax, x_coords, y_coords, boundaries, sector_analysis):
        """步骤4: 扇区边界标注"""
        # 绘制船舶分布
        ax.scatter(x_coords, y_coords, c=self.colors['ship_points'], s=2, alpha=0.4)

        # 计算最远距离
        distances = np.sqrt(x_coords**2 + y_coords**2)
        max_distance = np.max(distances)

        # 从扇区分析中获取实际的扇区角度范围
        sector_angles = []
        for sector_name, sector_data in sector_analysis.items():
            if sector_data is not None and 'angle_range' in sector_data:
                start_angle, end_angle = sector_data['angle_range']
                sector_angles.append(start_angle)

        # 如果没有角度信息，使用默认的8扇区划分
        if not sector_angles:
            num_sectors = 8
            sector_angles = [i * 2 * np.pi / num_sectors - np.pi for i in range(num_sectors)]
        else:
            sector_angles = sorted(list(set(sector_angles)))

        # 绘制扇区分割线
        for angle in sector_angles:
            x_end = max_distance * 1.1 * np.cos(angle)
            y_end = max_distance * 1.1 * np.sin(angle)

            ax.plot([0, x_end], [0, y_end],
                   color=self.colors['sector_lines'], linestyle='--', linewidth=1, alpha=0.5)

        # 绘制扇区边界点和距离标注
        for sector_name, boundary_info in boundaries.items():
            boundary_distance = boundary_info['boundary_distance']
            boundary_angle = boundary_info['boundary_angle']

            # 边界点坐标
            bx = boundary_distance * np.cos(boundary_angle)
            by = boundary_distance * np.sin(boundary_angle)

            # 绘制边界点
            ax.plot(bx, by, 'o', color=self.colors['boundary_points'],
                   markersize=8, markeredgewidth=2, markerfacecolor='white')

            # 标注距离
            text_x = bx * 1.15
            text_y = by * 1.15
            ax.text(text_x, text_y, f'{boundary_distance:.0f}m',
                   ha='center', va='center', fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))

        # 标记本船位置
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow', markeredgewidth=2)

        ax.set_xlim(-max_distance*1.3, max_distance*1.3)
        ax.set_ylim(-max_distance*1.3, max_distance*1.3)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('横向距离 (m)')
        ax.set_ylabel('纵向距离 (m)')

    def _plot_step5_ship_domain(self, ax, x_coords, y_coords, boundaries, ellipse, sector_analysis):
        """步骤5: 船舶领域椭圆"""
        # 绘制船舶分布（更淡）
        ax.scatter(x_coords, y_coords, c=self.colors['ship_points'], s=2, alpha=0.3)

        # 计算最远距离
        distances = np.sqrt(x_coords**2 + y_coords**2)
        max_distance = np.max(distances)

        # 绘制扇区分割线（更淡）
        # 从扇区分析中获取实际的扇区角度范围
        sector_angles = []
        for sector_name, sector_data in sector_analysis.items():
            if sector_data is not None and 'angle_range' in sector_data:
                start_angle, end_angle = sector_data['angle_range']
                sector_angles.append(start_angle)

        # 如果没有角度信息，使用默认的8扇区划分
        if not sector_angles:
            num_sectors = 8
            sector_angles = [i * 2 * np.pi / num_sectors - np.pi for i in range(num_sectors)]
        else:
            sector_angles = sorted(list(set(sector_angles)))

        for angle in sector_angles:
            x_end = max_distance * 1.1 * np.cos(angle)
            y_end = max_distance * 1.1 * np.sin(angle)

            ax.plot([0, x_end], [0, y_end],
                   color=self.colors['sector_lines'], linestyle='--', linewidth=1, alpha=0.3)

        # 绘制扇区边界点（更小）
        for sector_name, boundary_info in boundaries.items():
            boundary_distance = boundary_info['boundary_distance']
            boundary_angle = boundary_info['boundary_angle']

            bx = boundary_distance * np.cos(boundary_angle)
            by = boundary_distance * np.sin(boundary_angle)

            ax.plot(bx, by, 'o', color=self.colors['boundary_points'],
                   markersize=6, alpha=0.7)

        # 绘制船舶领域椭圆（粉色实线）
        if ellipse and 'a' in ellipse and 'b' in ellipse:
            a, b = ellipse['a'], ellipse['b']

            # 生成椭圆点
            theta = np.linspace(0, 2*np.pi, 100)
            ellipse_x = b * np.cos(theta)  # 横向半轴
            ellipse_y = a * np.sin(theta)  # 纵向半轴

            ax.plot(ellipse_x, ellipse_y, color=self.colors['ellipse'],
                   linewidth=3, label=f'船舶领域 (a={a:.0f}m, b={b:.0f}m)')

            # 标注椭圆参数
            ax.text(0.02, 0.98, f'长半轴: {a:.0f}m\n短半轴: {b:.0f}m\n长短比: {a/b:.2f}',
                   transform=ax.transAxes, va='top', ha='left',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='lightpink', alpha=0.8))

        # 标记本船位置
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow', markeredgewidth=2)

        ax.set_xlim(-max_distance*1.1, max_distance*1.1)
        ax.set_ylim(-max_distance*1.1, max_distance*1.1)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_xlabel('横向距离 (m)')
        ax.set_ylabel('纵向距离 (m)')

    def create_summary_comparison(self):
        """创建不同场景和船长的对比图"""
        print("\n=== 生成对比分析 ===")

        vis_dir = Path("vis/comprehensive_visualization")
        vis_dir.mkdir(parents=True, exist_ok=True)

        # 按场景类型分组
        crossing_scenes = {k: v for k, v in self.ellipse_params.items() if k.startswith('交叉')}
        overtaking_scenes = {k: v for k, v in self.ellipse_params.items() if k.startswith('追越')}

        if crossing_scenes:
            self._create_scenario_comparison('交叉避让', crossing_scenes, vis_dir)

        if overtaking_scenes:
            self._create_scenario_comparison('追越避让', overtaking_scenes, vis_dir)

    def _create_scenario_comparison(self, scenario_name, scenes, vis_dir):
        """创建单个场景类型的对比图"""
        n_scenes = len(scenes)
        if n_scenes == 0:
            return

        # 计算子图布局
        cols = min(3, n_scenes)
        rows = (n_scenes + cols - 1) // cols

        fig, axes = plt.subplots(rows, cols, figsize=(6*cols, 6*rows))
        if n_scenes == 1:
            axes = [axes]
        elif rows == 1:
            axes = [axes]
        else:
            axes = axes.flatten()

        fig.suptitle(f'{scenario_name}不同船长的船舶领域对比', fontsize=16, fontweight='bold')

        for i, (scene_key, ellipse) in enumerate(scenes.items()):
            ax = axes[i] if n_scenes > 1 else axes[0]

            # 获取数据
            if scene_key in self.density_results:
                density_data = self.density_results[scene_key]
                x_coords = density_data['x_coords']
                y_coords = density_data['y_coords']

                # 绘制数据点
                ax.scatter(x_coords, y_coords, c=self.colors['ship_points'], s=1, alpha=0.4)

            # 绘制椭圆
            if 'a' in ellipse and 'b' in ellipse:
                a, b = ellipse['a'], ellipse['b']

                theta = np.linspace(0, 2*np.pi, 100)
                ellipse_x = b * np.cos(theta)
                ellipse_y = a * np.sin(theta)

                ax.plot(ellipse_x, ellipse_y, color=self.colors['ellipse'], linewidth=3)

                # 标注
                _, length_interval = scene_key.split('_', 1)
                ax.set_title(f'{length_interval}\na={a:.0f}m, b={b:.0f}m')

            # 标记本船
            ax.plot(0, 0, 'ko', markersize=6, markerfacecolor='yellow')

            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            ax.set_xlabel('横向距离 (m)')
            ax.set_ylabel('纵向距离 (m)')

        # 隐藏多余的子图
        for i in range(n_scenes, len(axes)):
            axes[i].axis('off')

        plt.tight_layout()

        # 保存
        filename = f"{scenario_name}_comparison.png"
        save_path = vis_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 {scenario_name}对比图保存至: {save_path}")

    def create_single_visualization(self, scene_key, step=None, save_dir=None):
        """创建单个场景的指定步骤可视化

        Args:
            scene_key: 场景键名，如 "交叉_100m以下"
            step: 可视化步骤 (1-5)，None表示生成所有步骤
            save_dir: 保存目录，None使用默认目录
        """
        print(f"\n=== 生成单独可视化: {scene_key} ===")

        if scene_key not in self.density_results:
            print(f"❌ 未找到场景数据: {scene_key}")
            available_scenes = list(self.density_results.keys())
            print(f"可用场景: {available_scenes}")
            return False

        # 设置保存目录
        if save_dir is None:
            save_dir = Path("vis/single_visualization")
        else:
            save_dir = Path(save_dir)
        save_dir.mkdir(parents=True, exist_ok=True)

        # 获取数据
        density_data = self.density_results[scene_key]
        boundaries = self.sector_boundaries.get(scene_key, {})
        ellipse = self.ellipse_params.get(scene_key, {})

        x_coords = density_data['x_coords']
        y_coords = density_data['y_coords']
        sector_analysis = density_data['sector_analysis']

        if step is None:
            # 生成所有步骤的综合图
            self._create_single_comprehensive_plot(scene_key, x_coords, y_coords,
                                                 boundaries, ellipse, sector_analysis, save_dir)
        else:
            # 生成指定步骤的单独图
            self._create_single_step_plot(scene_key, step, x_coords, y_coords,
                                        boundaries, ellipse, sector_analysis, save_dir)

        return True

    def _create_single_comprehensive_plot(self, scene_key, x_coords, y_coords,
                                        boundaries, ellipse, sector_analysis, save_dir):
        """创建单个场景的综合图（所有5个步骤）"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'{scene_key.replace("_", " - ")} 船舶领域分析', fontsize=16, fontweight='bold')

        # 步骤1-5
        self._plot_step1_ship_distribution(axes[0, 0], x_coords, y_coords)
        axes[0, 0].set_title('步骤1: 船舶分布+最远圆+坐标轴')

        self._plot_step2_sector_division(axes[0, 1], x_coords, y_coords, sector_analysis)
        axes[0, 1].set_title('步骤2: 扇区划分')

        self._plot_step3_density_distance(axes[0, 2], sector_analysis)
        axes[0, 2].set_title('步骤3: 密度随距离变化')

        self._plot_step4_sector_boundaries(axes[1, 0], x_coords, y_coords, boundaries, sector_analysis)
        axes[1, 0].set_title('步骤4: 扇区边界标注')

        self._plot_step5_ship_domain(axes[1, 1], x_coords, y_coords, boundaries, ellipse, sector_analysis)
        axes[1, 1].set_title('步骤5: 船舶领域椭圆')

        # 隐藏第6个子图
        axes[1, 2].axis('off')

        plt.tight_layout()

        # 保存
        scenario_type, length_interval = scene_key.split('_', 1)
        filename = f"{scenario_type}_{length_interval}_comprehensive.png"
        save_path = save_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 综合图保存至: {save_path}")

    def _create_single_step_plot(self, scene_key, step, x_coords, y_coords,
                               boundaries, ellipse, sector_analysis, save_dir):
        """创建单个步骤的独立图"""
        fig, ax = plt.subplots(1, 1, figsize=(10, 10))

        step_names = {
            1: "船舶分布+最远圆+坐标轴",
            2: "扇区划分",
            3: "密度随距离变化",
            4: "扇区边界标注",
            5: "船舶领域椭圆"
        }

        if step == 1:
            self._plot_step1_ship_distribution(ax, x_coords, y_coords)
        elif step == 2:
            self._plot_step2_sector_division(ax, x_coords, y_coords, sector_analysis)
        elif step == 3:
            self._plot_step3_density_distance(ax, sector_analysis)
        elif step == 4:
            self._plot_step4_sector_boundaries(ax, x_coords, y_coords, boundaries, sector_analysis)
        elif step == 5:
            self._plot_step5_ship_domain(ax, x_coords, y_coords, boundaries, ellipse, sector_analysis)
        else:
            print(f"❌ 无效的步骤编号: {step}，有效范围: 1-5")
            plt.close()
            return False

        ax.set_title(f'{scene_key.replace("_", " - ")} - 步骤{step}: {step_names[step]}',
                    fontsize=14, fontweight='bold')

        plt.tight_layout()

        # 保存
        scenario_type, length_interval = scene_key.split('_', 1)
        filename = f"{scenario_type}_{length_interval}_step{step}.png"
        save_path = save_dir / filename
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 步骤{step}图保存至: {save_path}")
        return True

    def list_available_scenes(self):
        """列出所有可用的场景"""
        print("\n=== 可用场景列表 ===")

        if not self.density_results:
            print("❌ 未加载任何场景数据")
            return []

        scenes = list(self.density_results.keys())

        print("场景键名:")
        for i, scene in enumerate(scenes, 1):
            scenario_type, length_interval = scene.split('_', 1)
            data_count = len(self.density_results[scene]['x_coords'])
            has_boundaries = scene in self.sector_boundaries
            has_ellipse = scene in self.ellipse_params

            status = "✅" if (has_boundaries and has_ellipse) else "⚠️"

            print(f"  {i:2d}. {scene:20} ({scenario_type} - {length_interval}) "
                  f"[{data_count:4d}点] {status}")

        print(f"\n总计: {len(scenes)} 个场景")
        print("✅ = 完整数据 (密度+边界+椭圆)")
        print("⚠️ = 数据不完整")

        return scenes

    def run_full_visualization(self):
        """运行完整的可视化流程"""
        print("🎨 开始船舶领域可视化...")
        print("=" * 60)

        try:
            # 加载数据
            if not self.load_data():
                return False

            # 生成完整可视化
            self.create_comprehensive_visualization()

            # 生成对比分析
            self.create_summary_comparison()

            print("\n" + "=" * 60)
            print("🎉 船舶领域可视化完成！")
            print("📁 输出目录: vis/comprehensive_visualization/")
            print("=" * 60)

            return True

        except Exception as e:
            print(f"\n❌ 可视化失败: {e}")
            if self.debug:
                import traceback
                traceback.print_exc()
            return False


def main():
    """主函数 - 使用控制参数"""
    print("🎨 船舶领域可视化系统")
    print("=" * 50)

    # 显示当前控制参数
    print("📋 当前控制参数:")
    print(f"   目标场景: {TARGET_SCENE or '所有场景'}")
    print(f"   目标步骤: {TARGET_STEP or '综合图(所有步骤)'}")
    print(f"   生成所有: {'是' if GENERATE_ALL else '否'}")
    print(f"   生成对比: {'是' if GENERATE_COMPARISON else '否'}")
    print("=" * 50)

    # 创建可视化器
    visualizer = ShipDomainVisualizer(debug=DEBUG_MODE)

    # 加载数据
    if not visualizer.load_data():
        print("❌ 数据加载失败")
        return

    # 根据控制参数执行相应操作
    if GENERATE_ALL:
        # 生成所有场景的完整可视化
        print("\n🎨 生成所有场景的完整可视化...")
        success = visualizer.run_full_visualization()
        if success:
            print("✅ 所有场景可视化生成成功")
        else:
            print("❌ 可视化生成失败")

    elif TARGET_SCENE:
        # 生成指定场景的可视化
        print(f"\n🎯 生成指定场景可视化: {TARGET_SCENE}")
        success = visualizer.create_single_visualization(TARGET_SCENE, TARGET_STEP)
        if success:
            if TARGET_STEP is None:
                print(f"✅ {TARGET_SCENE} 的综合可视化生成成功")
            else:
                print(f"✅ {TARGET_SCENE} 的步骤{TARGET_STEP}可视化生成成功")
        else:
            print(f"❌ {TARGET_SCENE} 的可视化生成失败")

    else:
        # 显示可用场景列表
        print("\n📋 未指定目标场景，显示可用场景:")
        visualizer.list_available_scenes()
        print("\n💡 提示: 修改代码顶部的 TARGET_SCENE 参数来指定要可视化的场景")

    # 生成对比图
    if GENERATE_COMPARISON and (GENERATE_ALL or not TARGET_SCENE):
        print("\n📊 生成对比分析图...")
        visualizer.create_summary_comparison()

    print("\n🎉 可视化任务完成！")
    print("📁 输出目录:")
    if GENERATE_ALL or not TARGET_SCENE:
        print("   • vis/comprehensive_visualization/ (完整分析)")
    if TARGET_SCENE:
        print("   • vis/single_visualization/ (单独场景)")

    print("\n💡 修改提示:")
    print("   要生成其他场景，请修改代码顶部的控制参数：")
    print("   • TARGET_SCENE: 指定场景名称")
    print("   • TARGET_STEP: 指定步骤(1-5)或None(综合图)")
    print("   • GENERATE_ALL: True生成所有场景")
    print("   • GENERATE_COMPARISON: True生成对比图")


if __name__ == '__main__':
    main()
