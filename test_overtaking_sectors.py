"""
测试追越避让的扇区处理逻辑
验证是否正确过滤了后方扇区（3、4象限）
"""

import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def calculate_sector_angles(num_sectors=8):
    """计算扇区角度范围"""
    sector_width = 2 * np.pi / num_sectors
    angles = []
    
    for i in range(num_sectors):
        start_angle = i * sector_width - np.pi  # 从-π开始
        end_angle = (i + 1) * sector_width - np.pi
        angles.append((start_angle, end_angle, f"扇区{i+1}"))
    
    return angles

def test_sector_filtering():
    """测试扇区过滤逻辑"""
    print("🔍 测试追越避让扇区过滤逻辑")
    print("=" * 50)
    
    # 计算8个扇区
    sector_angles = calculate_sector_angles(8)
    
    print("所有扇区信息:")
    forward_sectors = []
    backward_sectors = []
    
    for i, (start_angle, end_angle, sector_name) in enumerate(sector_angles):
        # 扇区中心角度
        center_angle = (start_angle + end_angle) / 2
        if end_angle < start_angle:  # 跨越边界的情况
            center_angle = center_angle + np.pi if center_angle < 0 else center_angle - np.pi
        
        # 中心点坐标
        center_x = np.cos(center_angle)
        center_y = np.sin(center_angle)
        
        print(f"{sector_name}: 角度范围[{np.degrees(start_angle):.1f}°, {np.degrees(end_angle):.1f}°], "
              f"中心角度{np.degrees(center_angle):.1f}°, 中心Y={center_y:.3f}")
        
        # 判断是否为前方扇区
        if center_y > 0:
            forward_sectors.append((sector_name, center_angle, center_x, center_y))
            print(f"   ✅ 前方扇区 (Y > 0)")
        else:
            backward_sectors.append((sector_name, center_angle, center_x, center_y))
            print(f"   ❌ 后方扇区 (Y ≤ 0) - 追越时将被跳过")
    
    print(f"\n📊 扇区分类结果:")
    print(f"   前方扇区 (追越使用): {len(forward_sectors)}个")
    print(f"   后方扇区 (追越跳过): {len(backward_sectors)}个")
    
    # 可视化扇区分布
    visualize_sectors(forward_sectors, backward_sectors)
    
    return forward_sectors, backward_sectors

def visualize_sectors(forward_sectors, backward_sectors):
    """可视化扇区分布"""
    fig, ax = plt.subplots(1, 1, figsize=(10, 8))
    
    # 绘制坐标轴
    ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)
    ax.axvline(x=0, color='k', linestyle='-', alpha=0.3)
    
    # 绘制前方扇区
    for sector_name, angle, x, y in forward_sectors:
        ax.arrow(0, 0, x, y, head_width=0.05, head_length=0.05, 
                fc='green', ec='green', alpha=0.7)
        ax.text(x*1.2, y*1.2, sector_name, ha='center', va='center', 
               bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.7))
    
    # 绘制后方扇区
    for sector_name, angle, x, y in backward_sectors:
        ax.arrow(0, 0, x, y, head_width=0.05, head_length=0.05, 
                fc='red', ec='red', alpha=0.7)
        ax.text(x*1.2, y*1.2, sector_name, ha='center', va='center',
               bbox=dict(boxstyle='round,pad=0.3', facecolor='lightcoral', alpha=0.7))
    
    # 标注象限
    ax.text(0.7, 0.7, '第1象限\n(前右)', ha='center', va='center', fontsize=12, 
           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.5))
    ax.text(-0.7, 0.7, '第2象限\n(前左)', ha='center', va='center', fontsize=12,
           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.5))
    ax.text(-0.7, -0.7, '第3象限\n(后左)', ha='center', va='center', fontsize=12,
           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.5))
    ax.text(0.7, -0.7, '第4象限\n(后右)', ha='center', va='center', fontsize=12,
           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.5))
    
    ax.set_xlim(-1.5, 1.5)
    ax.set_ylim(-1.5, 1.5)
    ax.set_aspect('equal')
    ax.set_title('追越避让扇区过滤示意图\n绿色：使用的前方扇区，红色：跳过的后方扇区', fontsize=14)
    ax.set_xlabel('横向 (X)')
    ax.set_ylabel('纵向 (Y) - 船头方向')
    
    # 添加图例
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], color='green', lw=3, label='前方扇区 (追越使用)'),
        Line2D([0], [0], color='red', lw=3, label='后方扇区 (追越跳过)')
    ]
    ax.legend(handles=legend_elements, loc='upper right')
    
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('vis/overtaking_sector_filtering.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"📊 扇区分布图保存至: vis/overtaking_sector_filtering.png")

def test_boundary_point_filtering():
    """测试边界点过滤逻辑"""
    print(f"\n🔍 测试边界点过滤逻辑")
    print("=" * 50)
    
    # 模拟一些边界点（包括前方和后方）
    boundary_points = [
        (100, 200, "扇区1"),   # 前方右
        (-80, 150, "扇区2"),   # 前方左
        (-120, -100, "扇区3"), # 后方左
        (90, -80, "扇区4"),    # 后方右
        (150, 180, "扇区5"),   # 前方右
        (-100, 160, "扇区6"),  # 前方左
    ]
    
    print("原始边界点:")
    for x, y, sector in boundary_points:
        status = "前方" if y > 0 else "后方"
        print(f"   {sector}: ({x:4.0f}, {y:4.0f}) - {status}")
    
    # 过滤前方边界点
    forward_points = []
    for x, y, sector_name in boundary_points:
        if y > 0:  # 只保留前方的边界点（Y > 0）
            forward_points.append((x, y, sector_name))
        else:
            print(f"   过滤掉后方边界点: {sector_name} ({x:.1f}, {y:.1f})")
    
    print(f"\n过滤后的前方边界点:")
    for x, y, sector in forward_points:
        print(f"   {sector}: ({x:4.0f}, {y:4.0f}) - 前方")
    
    print(f"\n📊 边界点过滤结果:")
    print(f"   原始边界点: {len(boundary_points)}个")
    print(f"   前方边界点: {len(forward_points)}个")
    print(f"   过滤掉: {len(boundary_points) - len(forward_points)}个")

if __name__ == '__main__':
    # 创建输出目录
    import os
    os.makedirs('vis', exist_ok=True)
    
    # 运行测试
    forward_sectors, backward_sectors = test_sector_filtering()
    test_boundary_point_filtering()
    
    print(f"\n🎉 测试完成！")
    print(f"追越避让将使用 {len(forward_sectors)} 个前方扇区进行边界检测和椭圆拟合")
