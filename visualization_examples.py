"""
船舶领域可视化使用示例
展示如何通过修改控制参数来生成不同的可视化图表
"""

def example_usage():
    """展示不同的使用方式"""
    
    print("🎨 船舶领域可视化系统使用示例")
    print("=" * 60)
    
    examples = [
        {
            "name": "生成所有场景的完整可视化",
            "params": {
                "TARGET_SCENE": None,
                "TARGET_STEP": None,
                "GENERATE_ALL": True,
                "GENERATE_COMPARISON": True
            },
            "description": "生成所有场景的5步分析图和对比图"
        },
        {
            "name": "生成交叉避让100m以下的综合图",
            "params": {
                "TARGET_SCENE": "交叉_100m以下",
                "TARGET_STEP": None,
                "GENERATE_ALL": False,
                "GENERATE_COMPARISON": False
            },
            "description": "生成单个场景的5步综合分析图"
        },
        {
            "name": "生成追越避让100-200m的步骤5",
            "params": {
                "TARGET_SCENE": "追越_100-200m",
                "TARGET_STEP": 5,
                "GENERATE_ALL": False,
                "GENERATE_COMPARISON": False
            },
            "description": "只生成船舶领域椭圆图"
        },
        {
            "name": "生成交叉避让200m以上的步骤1",
            "params": {
                "TARGET_SCENE": "交叉_200m以上",
                "TARGET_STEP": 1,
                "GENERATE_ALL": False,
                "GENERATE_COMPARISON": False
            },
            "description": "只生成船舶分布+最远圆+坐标轴图"
        },
        {
            "name": "查看所有可用场景",
            "params": {
                "TARGET_SCENE": None,
                "TARGET_STEP": None,
                "GENERATE_ALL": False,
                "GENERATE_COMPARISON": False
            },
            "description": "列出所有可用的场景和数据状态"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n📋 示例 {i}: {example['name']}")
        print(f"   描述: {example['description']}")
        print("   参数设置:")
        for param, value in example['params'].items():
            print(f"     {param} = {repr(value)}")
        print("   " + "-" * 50)
    
    print(f"\n🔧 使用方法:")
    print("1. 打开 ship_domain_visualization.py")
    print("2. 修改文件顶部的控制参数")
    print("3. 运行: python ship_domain_visualization.py")
    
    print(f"\n📊 可视化步骤说明:")
    steps = {
        1: "船舶分布 + 最远距离圆 + 坐标轴直径",
        2: "扇区划分 (8个扇区的黑色虚线)",
        3: "各扇区密度随距离变化 (密度曲线图)",
        4: "扇区边界标注 (红色边界点+距离标签)",
        5: "船舶领域椭圆 (粉色椭圆+参数标注)"
    }
    
    for step, description in steps.items():
        print(f"   步骤{step}: {description}")
    
    print(f"\n🎯 常用场景名称:")
    common_scenes = [
        "交叉_100m以下",
        "交叉_100-200m", 
        "交叉_200m以上",
        "追越_100m以下",
        "追越_100-200m",
        "追越_200m以上"
    ]
    
    for scene in common_scenes:
        scenario_type, length_interval = scene.split('_', 1)
        print(f"   {scene:15} ({scenario_type}避让 - {length_interval})")
    
    print(f"\n💡 提示:")
    print("• 设置 TARGET_SCENE = None 可以查看所有可用场景")
    print("• 设置 TARGET_STEP = None 生成综合图(包含所有5个步骤)")
    print("• 设置 TARGET_STEP = 1-5 只生成指定步骤的单独图")
    print("• 设置 GENERATE_ALL = True 生成所有场景的完整分析")
    print("• 设置 GENERATE_COMPARISON = True 额外生成对比分析图")

def show_file_structure():
    """显示输出文件结构"""
    print(f"\n📁 输出文件结构:")
    print("vis/")
    print("├── comprehensive_visualization/     # 完整分析图")
    print("│   ├── 交叉_100m以下_comprehensive_analysis.png")
    print("│   ├── 追越_100-200m_comprehensive_analysis.png")
    print("│   ├── 交叉避让_comparison.png")
    print("│   └── 追越避让_comparison.png")
    print("└── single_visualization/           # 单独场景图")
    print("    ├── 交叉_100m以下_comprehensive.png")
    print("    ├── 交叉_100m以下_step1.png")
    print("    ├── 交叉_100m以下_step2.png")
    print("    ├── 交叉_100m以下_step3.png")
    print("    ├── 交叉_100m以下_step4.png")
    print("    └── 交叉_100m以下_step5.png")

def show_color_scheme():
    """显示颜色方案"""
    print(f"\n🎨 颜色方案:")
    colors = {
        "船舶数据点": "蓝色 (#1f77b4)",
        "最远距离圆": "黑色实线",
        "坐标轴直径": "红色虚线",
        "扇区分割线": "黑色虚线",
        "扇区边界点": "红色圆点",
        "距离标注": "黄色背景标签",
        "船舶领域椭圆": "粉色实线 (hotpink)",
        "本船位置": "黄色圆点+黑色边框"
    }
    
    for element, color in colors.items():
        print(f"   {element:12}: {color}")

def main():
    """主函数"""
    example_usage()
    show_file_structure()
    show_color_scheme()
    
    print(f"\n" + "=" * 60)
    print("🚀 现在可以根据需要修改 ship_domain_visualization.py 的控制参数并运行！")

if __name__ == '__main__':
    main()
