#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的船舶分类测试
验证新的3个船长区间是否正确工作
"""

def test_new_classification():
    """测试新的船舶分类"""
    print("=== 船舶分类重构测试 ===")
    
    # 新的船长区间定义
    length_intervals = [
        (0, 100, "100m以下"),
        (100, 200, "100-200m"),
        (200, 500, "200m以上")
    ]
    
    print("新的船长分类区间:")
    for min_len, max_len, interval_name in length_intervals:
        print(f"  {interval_name}: [{min_len}, {max_len}) 米")
    
    def get_length_interval(length):
        """船长区间判断逻辑"""
        if length <= 0 or length > 600:
            return None
        
        for i, (min_len, max_len, interval_name) in enumerate(length_intervals):
            if i == len(length_intervals) - 1:
                # 最后一个区间：包含上边界及以上所有船舶
                if length >= min_len:
                    return interval_name
            else:
                # 其他区间：左闭右开
                if min_len <= length < max_len:
                    return interval_name
        return None
    
    # 测试关键边界值
    test_cases = [
        (50, "100m以下"),
        (99.9, "100m以下"),
        (100, "100-200m"),
        (150, "100-200m"),
        (199.9, "100-200m"),
        (200, "200m以上"),    # 关键：200米应该归入"200m以上"
        (250, "200m以上"),
        (300, "200m以上"),
        (400, "200m以上"),
        (500, "200m以上"),
        (550, "200m以上"),    # 超过原定义但合理
    ]
    
    print("\n关键边界值测试:")
    all_passed = True
    
    for length, expected in test_cases:
        actual = get_length_interval(length)
        status = "✓" if actual == expected else "✗"
        
        if actual != expected:
            all_passed = False
        
        print(f"  {length:5.1f}m -> {actual or 'None':11} (期望: {expected}) {status}")
    
    # 重点测试200米以上的船舶
    print("\n200米以上船舶测试:")
    large_ships = [200, 220, 250, 300, 350, 400, 450, 500, 550]
    large_ship_passed = True
    
    for length in large_ships:
        classification = get_length_interval(length)
        status = "✓" if classification == "200m以上" else "✗"
        
        if classification != "200m以上":
            large_ship_passed = False
        
        print(f"  {length}m -> {classification} {status}")
    
    print(f"\n测试结果:")
    print(f"  边界值测试: {'通过' if all_passed else '失败'}")
    print(f"  200m以上测试: {'通过' if large_ship_passed else '失败'}")
    
    if all_passed and large_ship_passed:
        print("  ✓ 所有测试通过！200米以上船舶分类问题已修复")
        return True
    else:
        print("  ✗ 部分测试失败")
        return False

def show_classification_summary():
    """显示分类总结"""
    print("\n=== 船舶分类重构总结 ===")
    
    print("变更内容:")
    print("  原分类: 0-50m, 50-100m, 100-150m, 150-200m, 200-250m (5个区间)")
    print("  新分类: 100m以下, 100-200m, 200m以上 (3个区间)")
    
    print("\n改进效果:")
    print("  1. 简化分类: 5个区间 → 3个区间")
    print("  2. 扩大覆盖: 最大250m → 最大600m")
    print("  3. 更合理: 200米以上统一归类")
    print("  4. 易理解: 分类边界更直观")
    
    print("\n应用场景:")
    print("  • 100m以下: 小型船舶 (渔船、游艇、小货船)")
    print("  • 100-200m: 中型船舶 (中型货船、客船)")
    print("  • 200m以上: 大型船舶 (大型货船、集装箱船、油轮)")
    
    print("\n关键修复:")
    print("  • 200米船舶现在正确归入'200m以上'类别")
    print("  • 500米以上船舶不再被排除")
    print("  • 边界判断逻辑更加合理")

def main():
    """主函数"""
    success = test_new_classification()
    show_classification_summary()
    
    if success:
        print("\n🎉 船舶分类重构成功！")
        print("现在可以重新运行 probability_density_analysis.py 和 ship_domain_fitting.py")
    else:
        print("\n❌ 分类重构存在问题，需要进一步检查")

if __name__ == '__main__':
    main()
