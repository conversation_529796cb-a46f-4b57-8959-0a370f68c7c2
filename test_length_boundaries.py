"""
测试船长边界判断逻辑
验证200米以上的船舶是否正确分类
"""

def test_length_interval_logic():
    """测试船长区间判断逻辑"""
    print("🔍 测试船长区间边界判断")
    print("=" * 50)
    
    # 船长区间定义
    length_intervals = [
        (0, 100, "100m以下"),
        (100, 200, "100-200m"),
        (200, 500, "200m以上")  # 实际上应该包含200m及以上所有船舶
    ]
    
    def get_length_interval(length):
        """修复后的船长区间判断逻辑"""
        # 特殊处理：确保所有合理长度的船舶都能被分类
        if length <= 0 or length > 600:  # 排除不合理的长度
            return None
        
        for i, (min_len, max_len, interval_name) in enumerate(length_intervals):
            if i == len(length_intervals) - 1:
                # 最后一个区间：包含上边界及以上所有船舶
                if length >= min_len:
                    return interval_name
            else:
                # 其他区间：左闭右开
                if min_len <= length < max_len:
                    return interval_name
        
        return None
    
    # 测试关键边界值
    test_cases = [
        # 边界值测试
        (0, "100m以下"),      # 最小值
        (50, "100m以下"),     # 中间值
        (99.9, "100m以下"),   # 接近边界
        (100, "100-200m"),    # 边界值
        (150, "100-200m"),    # 中间值
        (199.9, "100-200m"),  # 接近边界
        (200, "200m以上"),    # 边界值 - 关键测试点
        (250, "200m以上"),    # 中间值
        (300, "200m以上"),    # 大型船舶
        (400, "200m以上"),    # 超大型船舶
        (500, "200m以上"),    # 边界值
        (550, "200m以上"),    # 超过原定义但合理
        
        # 异常值测试
        (-10, None),          # 负值
        (0, "100m以下"),      # 零值
        (700, None),          # 过大值
    ]
    
    print("测试用例结果:")
    print("船长(m)  | 期望分类    | 实际分类    | 结果")
    print("-" * 50)
    
    passed = 0
    total = len(test_cases)
    
    for length, expected in test_cases:
        actual = get_length_interval(length)
        status = "✅" if actual == expected else "❌"
        
        if actual == expected:
            passed += 1
        
        print(f"{length:7.1f} | {expected or 'None':11} | {actual or 'None':11} | {status}")
    
    print("-" * 50)
    print(f"测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    # 重点验证200米以上的船舶
    print(f"\n🎯 重点验证200米以上船舶分类:")
    large_ships = [200, 220, 250, 300, 350, 400, 450, 500, 550]
    
    for length in large_ships:
        classification = get_length_interval(length)
        status = "✅" if classification == "200m以上" else "❌"
        print(f"   {length}m船舶 → {classification} {status}")
    
    return passed == total

def test_data_distribution():
    """测试数据分布情况"""
    print(f"\n📊 测试数据分布情况")
    print("=" * 50)
    
    # 模拟真实船舶长度分布
    import numpy as np
    np.random.seed(42)
    
    # 生成不同类型船舶的长度数据
    ship_lengths = []
    
    # 小型船舶 (20-100m)
    small_ships = np.random.uniform(20, 100, 200)
    ship_lengths.extend(small_ships)
    
    # 中型船舶 (100-200m)
    medium_ships = np.random.uniform(100, 200, 150)
    ship_lengths.extend(medium_ships)
    
    # 大型船舶 (200-400m)
    large_ships = np.random.uniform(200, 400, 100)
    ship_lengths.extend(large_ships)
    
    # 超大型船舶 (400-600m)
    xlarge_ships = np.random.uniform(400, 600, 20)
    ship_lengths.extend(xlarge_ships)
    
    print(f"生成测试数据: {len(ship_lengths)} 艘船舶")
    print(f"长度范围: {min(ship_lengths):.1f}m - {max(ship_lengths):.1f}m")
    
    # 使用修复后的分类逻辑
    def get_length_interval(length):
        length_intervals = [
            (0, 100, "100m以下"),
            (100, 200, "100-200m"),
            (200, 500, "200m以上")
        ]
        
        if length <= 0 or length > 600:
            return None
        
        for i, (min_len, max_len, interval_name) in enumerate(length_intervals):
            if i == len(length_intervals) - 1:
                if length >= min_len:
                    return interval_name
            else:
                if min_len <= length < max_len:
                    return interval_name
        return None
    
    # 统计分类结果
    classification_stats = {
        "100m以下": 0,
        "100-200m": 0,
        "200m以上": 0,
        "未分类": 0
    }
    
    for length in ship_lengths:
        classification = get_length_interval(length)
        if classification:
            classification_stats[classification] += 1
        else:
            classification_stats["未分类"] += 1
    
    print(f"\n分类统计结果:")
    total_ships = len(ship_lengths)
    for category, count in classification_stats.items():
        percentage = count / total_ships * 100
        print(f"   {category}: {count}艘 ({percentage:.1f}%)")
    
    # 验证200米以上船舶是否都被正确分类
    large_ship_lengths = [l for l in ship_lengths if l >= 200]
    large_ship_classified = [l for l in large_ship_lengths if get_length_interval(l) == "200m以上"]
    
    print(f"\n🔍 200米以上船舶验证:")
    print(f"   应分类船舶: {len(large_ship_lengths)}艘")
    print(f"   实际分类: {len(large_ship_classified)}艘")
    print(f"   分类准确率: {len(large_ship_classified)/len(large_ship_lengths)*100:.1f}%")
    
    if len(large_ship_classified) == len(large_ship_lengths):
        print("   ✅ 所有200米以上船舶都被正确分类")
        return True
    else:
        print("   ❌ 部分200米以上船舶未被正确分类")
        return False

def main():
    """主函数"""
    print("🚢 船长边界判断测试")
    print("=" * 60)
    
    # 运行测试
    test1_passed = test_length_interval_logic()
    test2_passed = test_data_distribution()
    
    print(f"\n" + "=" * 60)
    if test1_passed and test2_passed:
        print("🎉 所有测试通过！200米以上船舶分类问题已修复")
        print(f"\n✅ 修复要点:")
        print("   1. 最后一个区间使用 >= 判断，包含所有大型船舶")
        print("   2. 扩大合理长度范围到600米")
        print("   3. 确保边界值正确归类")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    print(f"\n📋 分类规则总结:")
    print("   • 100m以下: [0, 100) 米")
    print("   • 100-200m: [100, 200) 米") 
    print("   • 200m以上: [200, ∞) 米 (实际限制600米)")

if __name__ == '__main__':
    main()
