"""
测试可视化逻辑
验证5步可视化的核心功能
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def generate_test_data():
    """生成测试数据"""
    np.random.seed(42)
    
    # 模拟船舶分布数据
    n_points = 200
    
    # 椭圆形分布
    angles = np.random.uniform(0, 2*np.pi, n_points)
    radii = np.random.gamma(2, 50, n_points)  # 伽马分布模拟距离
    
    x_coords = radii * np.cos(angles) + np.random.normal(0, 10, n_points)
    y_coords = radii * np.sin(angles) + np.random.normal(0, 10, n_points)
    
    # 模拟扇区边界
    boundaries = {}
    for i in range(8):
        angle = i * 2 * np.pi / 8 - np.pi
        distance = np.random.uniform(80, 150)
        
        boundaries[f'扇区{i+1}'] = {
            'boundary_distance': distance,
            'boundary_angle': angle
        }
    
    # 模拟椭圆参数
    ellipse = {
        'a': 120.0,  # 长半轴
        'b': 80.0    # 短半轴
    }
    
    return x_coords, y_coords, boundaries, ellipse

def test_step1_visualization():
    """测试步骤1: 船舶分布 + 最远距离圆 + 坐标轴"""
    print("🔍 测试步骤1: 船舶分布可视化")
    
    x_coords, y_coords, _, _ = generate_test_data()
    
    fig, ax = plt.subplots(1, 1, figsize=(8, 8))
    
    # 绘制船舶分布（蓝色点）
    ax.scatter(x_coords, y_coords, c='#1f77b4', s=2, alpha=0.6, label='周围船舶')
    
    # 计算最远距离
    distances = np.sqrt(x_coords**2 + y_coords**2)
    max_distance = np.max(distances)
    
    # 绘制最远距离圆（黑色实线）
    circle = plt.Circle((0, 0), max_distance, fill=False, 
                       color='black', linewidth=2, label='最远距离圆')
    ax.add_patch(circle)
    
    # 绘制坐标轴直径（红色虚线）
    ax.plot([-max_distance, max_distance], [0, 0], 
            color='red', linestyle='--', linewidth=2, label='X轴直径')
    ax.plot([0, 0], [-max_distance, max_distance], 
            color='red', linestyle='--', linewidth=2, label='Y轴直径')
    
    # 标记本船位置
    ax.plot(0, 0, 'ko', markersize=10, markerfacecolor='yellow', 
           markeredgewidth=2, label='本船')
    
    ax.set_xlim(-max_distance*1.1, max_distance*1.1)
    ax.set_ylim(-max_distance*1.1, max_distance*1.1)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_xlabel('横向距离 (m)')
    ax.set_ylabel('纵向距离 (m)')
    ax.set_title('步骤1: 船舶分布 + 最远距离圆 + 坐标轴')
    
    # 保存测试图
    Path("vis").mkdir(exist_ok=True)
    plt.savefig('vis/test_step1.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 步骤1测试完成，最远距离: {max_distance:.1f}m")
    return True

def test_step2_visualization():
    """测试步骤2: 扇区划分"""
    print("🔍 测试步骤2: 扇区划分")
    
    x_coords, y_coords, _, _ = generate_test_data()
    
    fig, ax = plt.subplots(1, 1, figsize=(8, 8))
    
    # 绘制船舶分布
    ax.scatter(x_coords, y_coords, c='#1f77b4', s=2, alpha=0.6)
    
    # 计算最远距离
    distances = np.sqrt(x_coords**2 + y_coords**2)
    max_distance = np.max(distances)
    
    # 绘制扇区分割线（黑色虚线）
    num_sectors = 8
    for i in range(num_sectors):
        angle = i * 2 * np.pi / num_sectors - np.pi
        x_end = max_distance * 1.1 * np.cos(angle)
        y_end = max_distance * 1.1 * np.sin(angle)
        
        ax.plot([0, x_end], [0, y_end], 
               color='black', linestyle='--', linewidth=1, alpha=0.7)
        
        # 标注扇区编号
        text_x = max_distance * 0.8 * np.cos(angle)
        text_y = max_distance * 0.8 * np.sin(angle)
        ax.text(text_x, text_y, f'扇区{i+1}', ha='center', va='center',
               bbox=dict(boxstyle='round,pad=0.2', facecolor='lightblue', alpha=0.7))
    
    # 标记本船位置
    ax.plot(0, 0, 'ko', markersize=10, markerfacecolor='yellow', markeredgewidth=2)
    
    ax.set_xlim(-max_distance*1.1, max_distance*1.1)
    ax.set_ylim(-max_distance*1.1, max_distance*1.1)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_xlabel('横向距离 (m)')
    ax.set_ylabel('纵向距离 (m)')
    ax.set_title('步骤2: 扇区划分')
    
    plt.savefig('vis/test_step2.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 步骤2测试完成")
    return True

def test_step4_visualization():
    """测试步骤4: 扇区边界标注"""
    print("🔍 测试步骤4: 扇区边界标注")
    
    x_coords, y_coords, boundaries, _ = generate_test_data()
    
    fig, ax = plt.subplots(1, 1, figsize=(8, 8))
    
    # 绘制船舶分布
    ax.scatter(x_coords, y_coords, c='#1f77b4', s=2, alpha=0.4)
    
    # 计算最远距离
    distances = np.sqrt(x_coords**2 + y_coords**2)
    max_distance = np.max(distances)
    
    # 绘制扇区分割线
    num_sectors = 8
    for i in range(num_sectors):
        angle = i * 2 * np.pi / num_sectors - np.pi
        x_end = max_distance * 1.1 * np.cos(angle)
        y_end = max_distance * 1.1 * np.sin(angle)
        
        ax.plot([0, x_end], [0, y_end], 
               color='black', linestyle='--', linewidth=1, alpha=0.5)
    
    # 绘制扇区边界点和距离标注
    for sector_name, boundary_info in boundaries.items():
        boundary_distance = boundary_info['boundary_distance']
        boundary_angle = boundary_info['boundary_angle']
        
        # 边界点坐标
        bx = boundary_distance * np.cos(boundary_angle)
        by = boundary_distance * np.sin(boundary_angle)
        
        # 绘制边界点
        ax.plot(bx, by, 'o', color='red', 
               markersize=8, markeredgewidth=2, markerfacecolor='white')
        
        # 标注距离
        text_x = bx * 1.15
        text_y = by * 1.15
        ax.text(text_x, text_y, f'{boundary_distance:.0f}m', 
               ha='center', va='center', fontsize=9, fontweight='bold',
               bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.8))
    
    # 标记本船位置
    ax.plot(0, 0, 'ko', markersize=10, markerfacecolor='yellow', markeredgewidth=2)
    
    ax.set_xlim(-max_distance*1.3, max_distance*1.3)
    ax.set_ylim(-max_distance*1.3, max_distance*1.3)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_xlabel('横向距离 (m)')
    ax.set_ylabel('纵向距离 (m)')
    ax.set_title('步骤4: 扇区边界标注')
    
    plt.savefig('vis/test_step4.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 步骤4测试完成")
    return True

def test_step5_visualization():
    """测试步骤5: 船舶领域椭圆"""
    print("🔍 测试步骤5: 船舶领域椭圆")
    
    x_coords, y_coords, boundaries, ellipse = generate_test_data()
    
    fig, ax = plt.subplots(1, 1, figsize=(8, 8))
    
    # 绘制船舶分布（更淡）
    ax.scatter(x_coords, y_coords, c='#1f77b4', s=2, alpha=0.3)
    
    # 计算最远距离
    distances = np.sqrt(x_coords**2 + y_coords**2)
    max_distance = np.max(distances)
    
    # 绘制扇区分割线（更淡）
    num_sectors = 8
    for i in range(num_sectors):
        angle = i * 2 * np.pi / num_sectors - np.pi
        x_end = max_distance * 1.1 * np.cos(angle)
        y_end = max_distance * 1.1 * np.sin(angle)
        
        ax.plot([0, x_end], [0, y_end], 
               color='black', linestyle='--', linewidth=1, alpha=0.3)
    
    # 绘制扇区边界点（更小）
    for sector_name, boundary_info in boundaries.items():
        boundary_distance = boundary_info['boundary_distance']
        boundary_angle = boundary_info['boundary_angle']
        
        bx = boundary_distance * np.cos(boundary_angle)
        by = boundary_distance * np.sin(boundary_angle)
        
        ax.plot(bx, by, 'o', color='red', markersize=6, alpha=0.7)
    
    # 绘制船舶领域椭圆（粉色实线）
    a, b = ellipse['a'], ellipse['b']
    
    # 生成椭圆点
    theta = np.linspace(0, 2*np.pi, 100)
    ellipse_x = b * np.cos(theta)  # 横向半轴
    ellipse_y = a * np.sin(theta)  # 纵向半轴
    
    ax.plot(ellipse_x, ellipse_y, color='hotpink', 
           linewidth=3, label=f'船舶领域 (a={a:.0f}m, b={b:.0f}m)')
    
    # 标注椭圆参数
    ax.text(0.02, 0.98, f'长半轴: {a:.0f}m\n短半轴: {b:.0f}m\n长短比: {a/b:.2f}', 
           transform=ax.transAxes, va='top', ha='left',
           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightpink', alpha=0.8))
    
    # 标记本船位置
    ax.plot(0, 0, 'ko', markersize=10, markerfacecolor='yellow', markeredgewidth=2)
    
    ax.set_xlim(-max_distance*1.1, max_distance*1.1)
    ax.set_ylim(-max_distance*1.1, max_distance*1.1)
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_xlabel('横向距离 (m)')
    ax.set_ylabel('纵向距离 (m)')
    ax.set_title('步骤5: 船舶领域椭圆')
    
    plt.savefig('vis/test_step5.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 步骤5测试完成")
    return True

def create_comprehensive_test():
    """创建综合测试图"""
    print("🔍 创建综合测试图")
    
    x_coords, y_coords, boundaries, ellipse = generate_test_data()
    
    # 创建5个子图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('船舶领域可视化测试 - 5步分析', fontsize=16, fontweight='bold')
    
    # 步骤1-5的简化版本
    for step in range(1, 6):
        if step <= 2:
            ax = axes[0, step-1]
        else:
            ax = axes[1, step-3]
        
        # 基础绘制
        ax.scatter(x_coords, y_coords, c='#1f77b4', s=2, alpha=0.6)
        ax.plot(0, 0, 'ko', markersize=8, markerfacecolor='yellow', markeredgewidth=2)
        
        distances = np.sqrt(x_coords**2 + y_coords**2)
        max_distance = np.max(distances)
        
        if step == 1:
            # 最远距离圆 + 坐标轴
            circle = plt.Circle((0, 0), max_distance, fill=False, color='black', linewidth=2)
            ax.add_patch(circle)
            ax.plot([-max_distance, max_distance], [0, 0], 'r--', linewidth=2)
            ax.plot([0, 0], [-max_distance, max_distance], 'r--', linewidth=2)
            ax.set_title('步骤1: 分布+圆+轴')
            
        elif step == 2:
            # 扇区划分
            for i in range(8):
                angle = i * 2 * np.pi / 8 - np.pi
                x_end = max_distance * 1.1 * np.cos(angle)
                y_end = max_distance * 1.1 * np.sin(angle)
                ax.plot([0, x_end], [0, y_end], 'k--', linewidth=1, alpha=0.7)
            ax.set_title('步骤2: 扇区划分')
            
        elif step == 4:
            # 扇区边界
            for i in range(8):
                angle = i * 2 * np.pi / 8 - np.pi
                x_end = max_distance * 1.1 * np.cos(angle)
                y_end = max_distance * 1.1 * np.sin(angle)
                ax.plot([0, x_end], [0, y_end], 'k--', linewidth=1, alpha=0.5)
            
            for boundary_info in list(boundaries.values())[:4]:  # 只显示4个边界点
                bx = boundary_info['boundary_distance'] * np.cos(boundary_info['boundary_angle'])
                by = boundary_info['boundary_distance'] * np.sin(boundary_info['boundary_angle'])
                ax.plot(bx, by, 'ro', markersize=6)
            ax.set_title('步骤4: 扇区边界')
            
        elif step == 5:
            # 船舶领域椭圆
            a, b = ellipse['a'], ellipse['b']
            theta = np.linspace(0, 2*np.pi, 100)
            ellipse_x = b * np.cos(theta)
            ellipse_y = a * np.sin(theta)
            ax.plot(ellipse_x, ellipse_y, color='hotpink', linewidth=3)
            ax.set_title('步骤5: 船舶领域')
        
        ax.set_xlim(-max_distance*1.1, max_distance*1.1)
        ax.set_ylim(-max_distance*1.1, max_distance*1.1)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
    
    # 隐藏第6个子图
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig('vis/comprehensive_test.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ 综合测试图创建完成")
    return True

def main():
    """主函数"""
    print("🎨 可视化逻辑测试")
    print("=" * 40)
    
    # 创建输出目录
    Path("vis").mkdir(exist_ok=True)
    
    # 运行各步骤测试
    tests = [
        test_step1_visualization,
        test_step2_visualization,
        test_step4_visualization,
        test_step5_visualization,
        create_comprehensive_test
    ]
    
    passed = 0
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_func.__name__} 失败: {e}")
    
    print(f"\n🎉 测试完成: {passed}/{len(tests)} 个测试通过")
    print("📁 测试图片保存至: vis/")
    
    if passed == len(tests):
        print("✅ 可视化逻辑验证成功，可以运行完整系统")
    else:
        print("⚠️  部分测试失败，需要检查代码")

if __name__ == '__main__':
    main()
