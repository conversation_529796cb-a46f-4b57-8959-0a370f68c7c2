"""
船舶周围目标船分布可视化
显示交叉避让和追越避让场景下，不同船长区间的相对位置分布
配合13_ship_domain_fitting_simplified.py的输出
"""

import pickle
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import math
from pathlib import Path
from tqdm import tqdm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


class ShipDistributionVisualizer:
    """船舶分布可视化器"""
    
    def __init__(self, data_months=['2024_1', '2024_2', '2024_3']):
        """初始化可视化器"""
        self.data_months = data_months if isinstance(data_months, list) else [data_months]
        
        # 船长区间定义
        self.length_intervals = [
            (0, 50, "0-50m"),
            (50, 100, "50-100m"),
            (100, 150, "100-150m"),
            (150, 200, "150-200m"),
            (200, 250, "200-250m")
        ]
        
        # 机动时刻数据存储
        self.crossing_moments = []
        self.overtaking_moments = []
        
        # 按船型和场景类型分类的相对位置数据
        self.crossing_relative_data = {}
        self.overtaking_relative_data = {}
        
        # 椭圆拟合结果
        self.crossing_ellipse_params = {}
        self.overtaking_ellipse_params = {}
        
        print(f"🚢 船舶分布可视化器初始化完成")
        print(f"   数据月份: {self.data_months}")
        print(f"   船长区间: {[name for _, _, name in self.length_intervals]}")

    def load_maneuvering_moments(self):
        """加载机动时刻数据"""
        print("\n=== 加载机动时刻数据 ===")

        total_crossing = 0
        total_overtaking = 0

        for data_month in self.data_months:
            print(f"\n--- 加载 {data_month} 数据 ---")

            # 加载交叉避让机动时刻
            crossing_file = f'result/{data_month}/crossing_avoidance_scenes.pkl'
            if Path(crossing_file).exists():
                with open(crossing_file, 'rb') as f:
                    moments = pickle.load(f)
                self.crossing_moments.extend(moments)
                total_crossing += len(moments)
                print(f"   交叉避让: {len(moments)} 个时刻")
            else:
                print(f"⚠️  未找到: {crossing_file}")

            # 加载追越避让机动时刻（仅使用最后一个月）
            if data_month == self.data_months[-1]:
                overtaking_file = f'result/{data_month}/overtaking_avoidance_scenes.pkl'
                if Path(overtaking_file).exists():
                    with open(overtaking_file, 'rb') as f:
                        moments = pickle.load(f)
                    self.overtaking_moments.extend(moments)
                    total_overtaking += len(moments)
                    print(f"   追越避让: {len(moments)} 个时刻（仅使用最后月份）")
                else:
                    print(f"⚠️  未找到: {overtaking_file}")

        print(f"\n✅ 机动时刻加载完成:")
        print(f"   交叉避让: {total_crossing} 个")
        print(f"   追越避让: {total_overtaking} 个")

    def extract_relative_positions(self):
        """从机动时刻提取相对位置数据"""
        print("\n=== 提取相对位置数据 ===")

        # 初始化数据结构
        for _, _, interval_name in self.length_intervals:
            self.crossing_relative_data[interval_name] = []
            self.overtaking_relative_data[interval_name] = []

        # 处理交叉避让
        print("处理交叉避让机动时刻...")
        crossing_processed = self._process_moments(self.crossing_moments, self.crossing_relative_data)

        # 处理追越避让
        print("处理追越避让机动时刻...")
        overtaking_processed = self._process_moments(self.overtaking_moments, self.overtaking_relative_data)

        print(f"✅ 相对位置提取完成:")
        print(f"   交叉避让: {crossing_processed} 个有效相对位置")
        print(f"   追越避让: {overtaking_processed} 个有效相对位置")

        # 打印统计
        self._print_data_stats()

    def _process_moments(self, moments, target_data_dict):
        """处理机动时刻，提取相对位置"""
        processed_count = 0

        for moment in tqdm(moments, desc="提取相对位置"):
            try:
                # 提取机动船和目标船
                maneuvering_ship = moment[0]  # 机动船（本船）
                target_ship = moment[1]  # 被避让船（目标船）

                # 数据质量检查
                if not self._validate_ship_data(maneuvering_ship, target_ship):
                    continue

                # 获取船长区间
                own_length = maneuvering_ship.get('length', 100.0)
                interval_name = self._get_length_interval(own_length)

                if interval_name is None:
                    continue

                # 计算相对位置
                relative_pos = self._calculate_relative_position(maneuvering_ship, target_ship)

                if relative_pos is not None and self._validate_relative_position(relative_pos):
                    target_data_dict[interval_name].append(relative_pos)
                    processed_count += 1

            except Exception as e:
                continue

        return processed_count

    def _validate_ship_data(self, ship1, ship2):
        """验证船舶数据质量"""
        # 检查必要字段
        required_fields = ['mmsi', 'lon', 'lat', 'cog', 'sog', 'length']
        for ship in [ship1, ship2]:
            for field in required_fields:
                if field not in ship or ship[field] is None:
                    return False

        # 检查数值合理性
        if (abs(ship1['lon']) > 180 or abs(ship1['lat']) > 90 or
                abs(ship2['lon']) > 180 or abs(ship2['lat']) > 90):
            return False

        # 检查船舶尺寸合理性
        if (ship1['length'] <= 0 or ship1['length'] > 500 or
                ship2['length'] <= 0 or ship2['length'] > 500):
            return False

        # 检查速度合理性（0-30节）
        if (ship1['sog'] < 0 or ship1['sog'] > 30 or
                ship2['sog'] < 0 or ship2['sog'] > 30):
            return False

        return True

    def _validate_relative_position(self, relative_pos):
        """验证相对位置的合理性"""
        # 距离应该在合理范围内
        distance = relative_pos['distance']
        if distance < 20 or distance > 3000:
            return False

        # 相对位置不应该过于极端
        x, y = relative_pos['relative_x'], relative_pos['relative_y']
        if abs(x) > 2500 or abs(y) > 2500:
            return False

        return True

    def _calculate_relative_position(self, own_ship, target_ship):
        """计算相对位置"""
        try:
            # 经纬度转米（简化计算）
            lat_to_m = 111000
            lon_to_m = 111000 * math.cos(math.radians(own_ship['lat']))

            # 计算地理坐标差
            delta_lon = target_ship['lon'] - own_ship['lon']
            delta_lat = target_ship['lat'] - own_ship['lat']

            # 转为米制坐标
            x_geo = delta_lon * lon_to_m
            y_geo = delta_lat * lat_to_m

            # 转换到本船坐标系
            x_rel, y_rel = self._convert_to_ship_coordinate(x_geo, y_geo, own_ship['cog'])

            # 计算距离
            distance = math.sqrt(x_rel ** 2 + y_rel ** 2)

            return {
                'relative_x': x_rel,
                'relative_y': y_rel,
                'distance': distance,
                'own_ship_mmsi': own_ship['mmsi'],
                'target_ship_mmsi': target_ship['mmsi'],
                'own_ship_length': own_ship.get('length', 100.0),
                'time_point': own_ship['time_point']
            }

        except Exception as e:
            return None

    @staticmethod
    def _convert_to_ship_coordinate(x_geo, y_geo, heading):
        """转换到船舶坐标系（船头向前为Y轴正方向）"""
        heading_rad = math.radians(-heading)
        cos_h = math.cos(heading_rad)
        sin_h = math.sin(heading_rad)

        x_ship = cos_h * x_geo + sin_h * y_geo
        y_ship = -sin_h * x_geo + cos_h * y_geo

        return x_ship, y_ship

    def _get_length_interval(self, length):
        """根据船长获取区间名称"""
        for min_len, max_len, interval_name in self.length_intervals:
            if min_len <= length < max_len:
                return interval_name
        return None

    def _print_data_stats(self):
        """打印数据统计"""
        print(f"\n📊 相对位置数据统计:")
        for _, _, interval_name in self.length_intervals:
            crossing_count = len(self.crossing_relative_data[interval_name])
            overtaking_count = len(self.overtaking_relative_data[interval_name])
            print(f"   {interval_name}: 交叉{crossing_count}个, 追越{overtaking_count}个")

    def load_ellipse_parameters(self):
        """加载椭圆参数"""
        # 尝试加载简化版拟合器的椭圆参数
        simplified_file = 'result/simplified_fitting/ship_domain_fitting_results.pkl'
        if Path(simplified_file).exists():
            with open(simplified_file, 'rb') as f:
                results = pickle.load(f)
            
            self.crossing_ellipse_params = results.get('crossing_ellipse_params', {})
            self.overtaking_ellipse_params = results.get('overtaking_ellipse_params', {})
            
            print(f"✅ 加载椭圆参数: 交叉{len(self.crossing_ellipse_params)}个, 追越{len(self.overtaking_ellipse_params)}个")
            return True
        else:
            print(f"⚠️  未找到椭圆参数文件: {simplified_file}")
            return False

    def plot_ellipse(self, a, b, center=(0, 0), ax=None, **kwargs):
        """绘制椭圆"""
        if ax is None:
            ax = plt.gca()
        theta = np.linspace(0, 2 * np.pi, 100)
        x = b * np.cos(theta) + center[0]
        y = a * np.sin(theta) + center[1]
        ax.plot(x, y, **kwargs)

    def visualize_ship_distribution(self):
        """可视化船舶分布"""
        print("\n=== 可视化船舶分布 ===")
        
        # 船长区间
        length_intervals = [name for _, _, name in self.length_intervals]
        
        # 创建子图
        fig, axes = plt.subplots(2, 5, figsize=(20, 8))
        fig.suptitle('船舶周围目标船分布 (本船位于原点)', fontsize=16, fontweight='bold')
        
        # 绘制交叉避让场景
        for i, interval in enumerate(length_intervals):
            ax = axes[0, i]
            data = self.crossing_relative_data.get(interval, [])
            
            if data:
                # # 采样数据（避免点太多）
                # if len(data) > 2000:
                #     indices = np.random.choice(len(data), 2000, replace=False)
                #     sample_data = [data[idx] for idx in indices]
                # else:
                #     sample_data = data
                sample_data = data
                # 提取坐标
                x_coords = [d['relative_x'] for d in sample_data]
                y_coords = [d['relative_y'] for d in sample_data]
                
                # 绘制散点
                ax.scatter(x_coords, y_coords, alpha=0.3, s=1, c='blue', label=f'数据点({len(data)}个)')
                
                # 绘制椭圆
                if interval in self.crossing_ellipse_params and self.crossing_ellipse_params[interval] is not None:
                    ellipse = self.crossing_ellipse_params[interval]
                    self.plot_ellipse(ellipse['a'], ellipse['b'], ax=ax, color='red', linewidth=2, 
                                   label=f'安全域 {ellipse["a"]:.0f}×{ellipse["b"]:.0f}m')
            
            ax.set_title(f'交叉避让 - {interval}')
            ax.set_xlabel('横向距离 (m)')
            ax.set_ylabel('纵向距离 (m)')
            ax.grid(True, alpha=0.3)
            ax.set_aspect('equal')
            ax.set_xlim(-1000, 1000)
            ax.set_ylim(-1000, 1000)
            if data:
                ax.legend(fontsize=8)
        
        # 绘制追越避让场景
        for i, interval in enumerate(length_intervals):
            ax = axes[1, i]
            data = self.overtaking_relative_data.get(interval, [])
            
            if data:
                # 采样数据
                if len(data) > 2000:
                    indices = np.random.choice(len(data), 2000, replace=False)
                    sample_data = [data[idx] for idx in indices]
                else:
                    sample_data = data
                
                # 提取坐标
                x_coords = [d['relative_x'] for d in sample_data]
                y_coords = [d['relative_y'] for d in sample_data]
                
                # 绘制散点
                ax.scatter(x_coords, y_coords, alpha=0.3, s=1, c='green', label=f'数据点({len(data)}个)')
                
                # 绘制椭圆
                if interval in self.overtaking_ellipse_params and self.overtaking_ellipse_params[interval] is not None:
                    ellipse = self.overtaking_ellipse_params[interval]
                    self.plot_ellipse(ellipse['a'], ellipse['b'], ax=ax, color='red', linewidth=2,
                                   label=f'安全域 {ellipse["a"]:.0f}×{ellipse["b"]:.0f}m')
            
            ax.set_title(f'追越避让 - {interval}')
            ax.set_xlabel('横向距离 (m)')
            ax.set_ylabel('纵向距离 (m)')
            ax.grid(True, alpha=0.3)
            ax.set_aspect('equal')
            ax.set_xlim(-1000, 1000)
            ax.set_ylim(-1000, 1000)
            if data:
                ax.legend(fontsize=8)
        
        # 添加坐标系说明
        fig.text(0.02, 0.95, '坐标系: 本船位于原点，船头向上(+Y)，右舷向右(+X)', 
                 fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.9)
        
        # 保存图片
        output_file = 'vis/ship_distribution_simplified.png'
        Path('vis').mkdir(exist_ok=True)
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"可视化结果已保存: {output_file}")
        
        plt.show()

    def print_ellipse_summary(self):
        """打印椭圆参数摘要"""
        print("\n📊 椭圆参数摘要:")
        print("=" * 60)
        
        length_intervals = [name for _, _, name in self.length_intervals]
        
        print(f"{'船长区间':<10} {'交叉避让':<20} {'追越避让':<20}")
        print("-" * 60)
        
        for interval in length_intervals:
            # 交叉避让参数
            crossing_params = self.crossing_ellipse_params.get(interval)
            if crossing_params is not None:
                crossing_str = f"a={crossing_params['a']:.0f}m, b={crossing_params['b']:.0f}m"
            else:
                crossing_str = "拟合失败"
            
            # 追越避让参数
            overtaking_params = self.overtaking_ellipse_params.get(interval)
            if overtaking_params is not None:
                overtaking_str = f"a={overtaking_params['a']:.0f}m, b={overtaking_params['b']:.0f}m"
            else:
                overtaking_str = "拟合失败"
            
            print(f"{interval:<10} {crossing_str:<20} {overtaking_str:<20}")

    def print_data_summary(self):
        """打印数据摘要"""
        print("\n📊 数据统计摘要:")
        print("=" * 60)
        
        length_intervals = [name for _, _, name in self.length_intervals]
        
        print(f"{'船长区间':<10} {'交叉避让':<10} {'追越避让':<10} {'总计':<10}")
        print("-" * 50)
        
        total_crossing = 0
        total_overtaking = 0
        
        for interval in length_intervals:
            crossing_count = len(self.crossing_relative_data.get(interval, []))
            overtaking_count = len(self.overtaking_relative_data.get(interval, []))
            total = crossing_count + overtaking_count
            
            total_crossing += crossing_count
            total_overtaking += overtaking_count
            
            print(f"{interval:<10} {crossing_count:<10} {overtaking_count:<10} {total:<10}")
        
        print("-" * 50)
        print(f"{'总计':<10} {total_crossing:<10} {total_overtaking:<10} {total_crossing + total_overtaking:<10}")

    def run_full_pipeline(self):
        """运行完整流程"""
        print("🚢 开始船舶分布可视化流程...")
        print("=" * 60)

        # 执行流程
        self.load_maneuvering_moments()
        self.extract_relative_positions()
        self.load_ellipse_parameters()
        self.print_data_summary()
        self.print_ellipse_summary()
        self.visualize_ship_distribution()

        print("\n" + "=" * 60)
        print("🎉 船舶分布可视化完成！")
        print("=" * 60)

        return True


def main():
    """主函数"""
    data_months = ['2024_1', '2024_2', '2024_3']

    print(f"📊 船舶周围目标船分布可视化 (真实数据版)")
    print(f"   数据月份: {data_months}")
    print(f"   输入文件: crossing_avoidance_scenes.pkl, overtaking_avoidance_scenes.pkl")

    # 创建可视化器
    visualizer = ShipDistributionVisualizer(data_months=data_months)

    # 运行完整流程
    success = visualizer.run_full_pipeline()

    if success:
        print(f"\n📁 输出文件:")
        print(f"   可视化结果: vis/ship_distribution_simplified.png")
    else:
        print(f"\n❌ 可视化流程失败")


if __name__ == '__main__':
    main() 